<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/cube2equirect">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="world" type="Matrix4f" semantic="World" />
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj" />
  <Parameter name="tex_01" type="TextureCube" semantic="Texture0" />
  <Sampler name="sampler_tex">
    <Texture value="tex_01" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP" />
    <AddressV value="WRAP" />
    <AddressW value="WRAP" />
  </Sampler>
  <Shader type="glsl">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			uniform mat4 world;
			uniform mat4 vp;
		
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 
		
			varying vec2 tc_tex;
	
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
				tc_tex     = (a_uv0.xy + vec2(-0.25, 0)) * vec2(6.2831852, 3.1415926);			
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
        <![CDATA[
			precision mediump float;
		
			uniform samplerCube sampler_tex;

			varying vec2 tc_tex;
	
			void main()
			{ 
        vec3 normal = vec3(0.0, 0.0, 0.0);
        normal.y = cos(tc_tex.y);
        float r = sqrt(1.0 - normal.y * normal.y);
        normal.x = r * cos(tc_tex.x);
        normal.z = r * sin(tc_tex.x);
        normal = normalize(normal);
        
				gl_FragColor = textureCube(sampler_tex, normal) ;
			}
		]]></GLShader>
  </Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};

			in vec4 a_position; 
			in vec2 a_uv0; 
		
			out vec2 tc_tex;
	
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
				tc_tex     = (a_uv0.xy + vec2(-0.25, 0)) * vec2(6.2831852, 3.1415926);			
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
        <![CDATA[
			precision mediump float;
		
			uniform samplerCube sampler_tex;
		
			in vec2 tc_tex;
			out vec4 fragColor;
		
			void main()
			{ 
        vec3 normal = vec3(0.0, 0.0, 0.0);
        normal.y = cos(tc_tex.y);
        float r = sqrt(1.0 - normal.y * normal.y);
        normal.x = r * cos(tc_tex.x);
        normal.z = r * sin(tc_tex.x);
        normal = normalize(normal);
        
				fragColor = texture(sampler_tex, normal);
			}
		]]></GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h" /><![CDATA[
cbuffer cbEnt : register(b0)
{
    matrix world;
};
cbuffer cbView : register(b1)
{
    matrix vp;
};
struct VSInput
{
    float3 pos : POSITION;
    float2 uv : TEXCOORD;
};

struct VSOutput
{
    float4 pos:SV_POSITION;
    float2 uv: TEXCOORD0;
};

SamplerState sampler_tex    : register( s0 );
TextureCube tex_01      : register( t0 );

VSOutput VsMain(VSInput input)
{
    VSOutput o;
    o.pos = mul(float4(input.pos, 1), mul(world, vp));
    o.uv = (input.uv + float2(-0.25, 0)) * float2(6.2831852f, 3.1415926f);
    return o;
}

float4 PsMain(VSOutput input) :SV_TARGET
{
    float3 normal = float3(0, 0, 0);
    normal.y = cos(input.uv.y);
    float r = sqrt(1 - normal.y * normal.y);
    normal.x = r * cos(input.uv.x);
    normal.z = r * sin(input.uv.x);
    normal = normalize(normal);
    return tex_01.Sample(sampler_tex, normal);
}
		]]></Shader>
  <Technique name="cube2equirect">
    <Pass name="pass" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
</Effect>
