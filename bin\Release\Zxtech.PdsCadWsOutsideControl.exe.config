<?xml version="1.0"?>
<configuration>
  <system.serviceModel>
    <services>
      <service name="Zxtech.EDS.PDS.CadWsOutsideControl.CadWsOutsideControlService" behaviorConfiguration="CadWsBehavior">
        <host>
          <baseAddresses>
            <add baseAddress="net.tcp://localhost:530/CadService/" />
          </baseAddresses>
        </host>
        <endpoint address="Service" binding="netTcpBinding" bindingConfiguration="CadWsBinding" contract="Zxtech.EDS.PDS.CadWsOutsideControl.ICadWsOutsideControl" />
      </service>
    </services>
    <behaviors>
      <serviceBehaviors>
        <behavior name="CadWsBehavior">
          <serviceMetadata httpGetEnabled="True" httpGetUrl="http://localhost:8011/" />
          <serviceDebug includeExceptionDetailInFaults="true" />
          <dataContractSerializer maxItemsInObjectGraph="653566666" ignoreExtensionDataObject="true" />
        </behavior>
      </serviceBehaviors>
    </behaviors>
    <bindings>
      <netTcpBinding>
        <binding name="CadWsBinding" sendTimeout="00:10:00" maxReceivedMessageSize="6553600">
          <security mode="None" />
        </binding>
      </netTcpBinding>
    </bindings>
  </system.serviceModel>
  <appSettings>
    <add key="ReloadProeTime" value="2000" />
    <add key="IsAllowReloadProe" value="True" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
    <add key="CurrentLanguage" value="ja-JP" />
    <add key="AppName" value="monaker" />
  </appSettings>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager enabled="true" defaultProvider="ClientRoleProvider">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0" />
  </startup>
</configuration>