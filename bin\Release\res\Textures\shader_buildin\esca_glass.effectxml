<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/esca/esca_glass">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="world" type="Matrix4f" semantic="World" />
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj" />
  <Parameter name="v" type="Matrix4f" semantic="View" />
  <Parameter name="tex_ref" type="Texture2D" semantic="Texture0" />
  <Parameter name="emissive" type="Colorf" default="1,1,1,1" />
  <Parameter name="reflect_strength_diffuse" type="Colorf" default="1,1,1,1" />
  <Parameter name="tex_c" type="float32" />
  <Parameter name="tp" type="Matrix4f" />
  <Sampler name="s_ref">
    <Texture value="tex_ref" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="CLAMP" />
    <AddressV value="CLAMP" />
    <AddressW value="CLAMP" />
  </Sampler>
  <Shader type="glsl">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			uniform mat4 world;
			uniform mat4 vp;
			uniform mat4 v;
			uniform mat4 tp;
		
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 
		
			varying vec4 tc_ref;
		
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
	
				mat4 wvp1 = MUL(world, v) * tp;
				tc_ref = a_position * wvp1;
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel"><![CDATA[
			precision mediump float;
		
			uniform sampler2D  s_ref;
	
			uniform vec4 reflect_strength_diffuse;
			uniform vec4 emissive;
			
			uniform float tex_c;
		
			varying vec4 tc_ref;
		
			void main()
			{ 
				vec4 r1 = ScreenMapping(tc_ref);

				vec4 col_ref    = texture2D( s_ref,r1.xy ) ;
				vec4 col_r1     = col_ref ;
				float f           = 0.11 * col_ref.r + 0.59 * col_ref.g + 0.3 * col_ref.b;
				col_ref.x         =  col_ref.y = col_ref.z = f;
				col_ref           = lerp(col_r1,col_ref,tex_c);

				vec4  clr_res    = col_ref * emissive;
				clr_res.w  = reflect_strength_diffuse.x;
				gl_FragColor = clr_res;
			}
		]]></GLShader>
  </Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			layout (std140) uniform entity
			{
      mat4 world;
      };
			layout (std140) uniform view
			{
				mat4 v;
				mat4 vp;
			};
			layout (std140) uniform material
			{
				mat4 tp;
				vec4 reflect_strength_diffuse;
				vec4 emissive;
				float tex_c;
			};
		
			in vec4 a_position; 
			in vec2 a_uv0; 
		
			out vec4 tc_ref;
		
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
	
				mat4 wvp1 = MUL(world, v) * tp;
				tc_ref = a_position * wvp1;
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel"><![CDATA[
			precision mediump float;
		
			uniform sampler2D  s_ref;
	
			layout (std140) uniform material
			{
				mat4 tp;
				vec4 reflect_strength_diffuse;
				vec4 emissive;
				float tex_c;
			};
		
			in vec4 tc_ref;
			out vec4 fragColor;
		
			void main()
			{ 
				vec4 r1 = ScreenMapping(tc_ref);

				vec4 col_ref    = texture( s_ref,r1.xy ) ;
				vec4 col_r1     = col_ref ;
				float f           = 0.11 * col_ref.r + 0.59 * col_ref.g + 0.3 * col_ref.b;
				col_ref.x         =  col_ref.y = col_ref.z = f;
				col_ref           = lerp(col_r1,col_ref,tex_c);

				vec4  clr_res    = col_ref * emissive;
				clr_res.w  = reflect_strength_diffuse.x;
				fragColor = clr_res;
			}
		]]></GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h" /><![CDATA[
    cbuffer cbEntity : register( b0 )
    {
    matrix world;   
    };
    cbuffer cbView : register( b2 )
    {
    matrix v;   
    matrix vp;
    };
    
    cbuffer cbMaterial : register( b1 )
    {
		matrix tp;
		float4 reflect_strength_diffuse;
		float4 emissive;
		float tex_c;
    };
    
		SamplerState s_ref : register( s0 );
    Texture2D tex_ref : register( t0 );
	
		struct VsInput
		{
			float3     pos:POSITION;
			float2     tc :TEXCOORD; 
		};
    
		struct VsOutput
		{
			float4   pos             :   SV_POSITION ;
			float4   tc_ref          :   TEXCOORD0;
		};
    
		VsOutput VsMain(VsInput input)
		{
			VsOutput o = (VsOutput)0;
	
			float4   r0=mul(float4(input.pos,1),MUL(world, vp));
			o.pos=r0;
	
			float4x4 wvp1 = mul(MUL(world, v), tp);
			o.tc_ref     = mul(float4(input.pos,1),wvp1);

			return o;
		}
    
		float4 PsMain(VsOutput input) : SV_Target
		{
			float4 r1 = ScreenMapping(input.tc_ref);

			float4 col_ref    = tex_ref.Sample( s_ref,r1 ) ;
			float4 col_r1     = col_ref ;
			float f           = 0.11 * col_ref.r + 0.59 * col_ref.g + 0.3 * col_ref.b;
			col_ref.x         =  col_ref.y = col_ref.z = f;
			col_ref           = lerp(col_r1,col_ref,tex_c);

			float4  clr_res    = col_ref * emissive;
			clr_res.w  = reflect_strength_diffuse.x;
			return  clr_res;
		}
		]]></Shader>
  <Technique name="tech">
    <Pass name="pass" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
</Effect>
