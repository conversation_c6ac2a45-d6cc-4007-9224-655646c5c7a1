<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/texture_emissive_blend">
	<!--必须有name,type；semantic，default可选-->
	<Parameter name="vp" type="Matrix4f" semantic="ViewProj"/>
  <Parameter name="world" type="Matrix4f" semantic="World"/>
  <Parameter name="tex_01" type="Texture2D" semantic="Texture0"/>
	<Parameter name="tex_sel" type="Texture2D" semantic="Texture1"/>

  <Parameter name="focus_color" type="Colorf" default= "1,1,1,1" />
	<Parameter name="emissive" type="Colorf" default= "1,1,1,1" />

	<Parameter name="is_clip" type="float32" default="0" semantic="EffectGlobal"/>
	<Parameter name="clip_plane" type="Vector4f" default="0,1,0,0" semantic="EffectGlobal"/>

	<Sampler name="s_tex">
		<Texture value="tex_01"/>
		<Filter value="MIN_MAG_MIP_LINEAR"/>
		<AddressU value="WRAP"/>
		<AddressV value="WRAP"/>
		<AddressW value="WRAP"/>
	</Sampler>
	<Sampler name="s_sel">
		<Texture value="tex_sel"/>
		<Filter value="MIN_MAG_MIP_LINEAR"/>
		<AddressU value="CLAMP"/>
		<AddressV value="CLAMP"/>
		<AddressW value="CLAMP"/>
	</Sampler>	
	<Shader type="glsl">
		<Include file="shader_buildin/gl_inc.h"/>
		<GLShader name="VsMain" type="Vertex">
			<![CDATA[			
			uniform mat4 vp;
      uniform mat4 world;
			
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 
		
			varying vec4 tc_tex; 
			
			uniform mediump float is_clip;
			uniform vec4 clip_plane;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);				
				tc_tex.xy = a_uv0;	
		
				//if(is_clip > 0.0)
				//{
				//	vec4 world_pos = a_position * world;
				//	tc_tex.w = dot(world_pos, clip_plane);
				//}
			}
			]]>
		</GLShader>
		<GLShader name="PsMain" type="Pixel">
			<![CDATA[
			precision mediump float;
			
			uniform sampler2D s_tex;
			uniform sampler2D s_sel;
			
			uniform mediump float is_clip;
			uniform vec4 clip_plane;

			uniform vec4 emissive;
      uniform vec4 focus_color;
			
			varying vec4 tc_tex; 
	
			void main()
			{ 
				if(is_clip > 0.0 && tc_tex.w < 0.0)  { discard; }

				vec4 col_sel = texture2D( s_sel, tc_tex.xy) * focus_color;
				vec4 col_tex = texture2D( s_tex, tc_tex.xy) ;
				vec4 clr_res = col_tex * (1.0 - col_sel.w) + col_sel * col_sel.w; 
				//clr_res.w = 1.0;	
				
				gl_FragColor = clr_res * emissive;
			}
		]]>
		</GLShader>
	</Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h"/>
    <GLShader name="VsMain" type="Vertex">
      <![CDATA[			
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};			
			in vec4 a_position; 
			in vec2 a_uv0; 
		
			out vec4 tc_tex; 
			
			layout (std140) uniform global
			{
				vec4 clip_plane;
				mediump float is_clip;
			};
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);				
				tc_tex.xy = a_uv0;	
		
				//if(is_clip > 0.0)
				//{
				//	vec4 world_pos = a_position * world;
				//	tc_tex.w = dot(world_pos, clip_plane);
				//}
			}
			]]>
    </GLShader>
    <GLShader name="PsMain" type="Pixel">
      <![CDATA[
			precision mediump float;
			
			uniform sampler2D s_tex;
			uniform sampler2D s_sel;
			
			layout (std140) uniform global
			{
				vec4 clip_plane;
				mediump float is_clip;
			};

			layout (std140) uniform material
			{
				vec4 emissive;
				vec4 focus_color;
			};
			
			in vec4 tc_tex; 
			out vec4 fragColor;
	
			void main()
			{ 
				if(is_clip > 0.0 && tc_tex.w < 0.0)  { discard; }

				vec4 col_sel = texture( s_sel, tc_tex.xy) * focus_color;
				vec4 col_tex = texture( s_tex, tc_tex.xy) ;
				vec4 clr_res = col_tex * (1.0 - col_sel.w) + col_sel * col_sel.w; 
				//clr_res.w = 1.0;	
				
				fragColor = clr_res * emissive;
			}
		]]>
    </GLShader>
  </Shader>
  <Shader type="hlsl5">
		<Include file="shader_buildin/dx_inc.h"/>
		<![CDATA[
    cbuffer cbEntity : register( b0 )
    {
		matrix world;
    };
    cbuffer cbView : register( b3 )
    {
    matrix vp;
    };
    cbuffer cbMaterial : register( b1 )
    {
		float4 emissive;
    float4 focus_color;
    };
    
    cbuffer cbGlobal : register( b2 )
    {
    float4 clip_plane;
    float is_clip;
    };
	
    SamplerState s_tex : register( s0 );
    Texture2D tex_01 : register( t0 );
    
    SamplerState s_sel : register( s1 );
    Texture2D tex_sel : register( t1 );

		
		struct VsInput
		{
			float3     pos:POSITION;
			float2     tc :TEXCOORD; 
		};
    
		struct VsOutput
		{
			float4   pos             :   SV_POSITION ;
			float4   tc_tex          :   TEXCOORD0;
		};
    
		VsOutput VsMain(VsInput input)
		{
			VsOutput o = (VsOutput)0;
		
			o.pos=mul(float4(input.pos,1),MUL(world, vp));
			o.tc_tex.xy  = input.tc;	
		
			//if(is_clip > 0)
			//{
				//float4 world_pos = mul(float4(input.pos, 1), world);
				//o.tc_tex.w = dot(world_pos, clip_plane);
			//}
		
			return o;
		}
    
		float4 PsMain(VsOutput input) : SV_Target
		{
			if(is_clip > 0)  { clip(input.tc_tex.w); }

			float4 col_sel        = tex_sel.Sample( s_sel,input.tc_tex) * focus_color;      
			float4 col_tex        = tex_01.Sample( s_tex,input.tc_tex) ;
			float4 clr_res       = col_tex * (1 - col_sel.w) + col_sel * col_sel.w; 
			//clr_res.w           = 1.0;
			return  clr_res * emissive;
		}
		]]>
	</Shader>
	<Technique name="tech">
		<Pass name="pass" restorestate="false">
			<State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0"/>
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0"/>
		</Pass>
	</Technique>
</Effect>
