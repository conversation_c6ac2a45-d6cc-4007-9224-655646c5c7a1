﻿<?xml version="1.0" encoding="utf-8"?>
<CADTaskClientConfig>
  <MakeProjectMap>true</MakeProjectMap>
  <MakeSpreadMap>true</MakeSpreadMap>
  <IsDXFBendLines>false</IsDXFBendLines>
  <IsDXFReadThickness>true</IsDXFReadThickness>
  <MakeDWG>true</MakeDWG>
  <MakePDF>true</MakePDF>
  <ExpendViewName>ExpendViewName</ExpendViewName>
  <AttachmentType>PDF;DWG;JPG;zip;mp4;</AttachmentType>
  <IsHaveAttachment>true</IsHaveAttachment>
  <SaveObject>1</SaveObject>
  <!-- PdsPath -->
  <PdsPath>D:\NEUXA\CADBIM</PdsPath>
  <!-- WorkPath -->
  <WorkPath>D:\SDSWorkDir</WorkPath>
  <BakPath>D:\SDSWorkDir</BakPath>
  <!--DXF-->
  <IsCopyDXF>false</IsCopyDXF>
  <DxfCopyPath>C:\epd_workdir_sw</DxfCopyPath>
  <!-- TaskType 1200锛?100锛? -->
  <TaskType>1200</TaskType>
  <CreateUser></CreateUser>
  <WCFCADServer>net.tcp://localhost:8080/CADWCFServer</WCFCADServer>
  <WCFEdsServer>net.tcp://*************:602/Task/TaskService</WCFEdsServer>
  <!-- SendMailServer -->
  <SendMailServer>http://*************:8782/MsgCenter/</SendMailServer>
  <!-- IsUpdateModel -->
  <IsUpdateModel>true</IsUpdateModel>
  <UpdateModelServer>net.tcp://127.0.0.1:1526/UpdateModelService</UpdateModelServer>
  <SelectPTId></SelectPTId>
  <DWGVersion>2010</DWGVersion>
  <DeleteUnuseConfig>false</DeleteUnuseConfig>
  <FactoryNo></FactoryNo>
  <IsSendMail>false</IsSendMail>
   <IsReturnCADstatus>true</IsReturnCADstatus>
  <SendbackUrl>http://*************:8070/ecs/DecorationWebApi/QuickTaskOverRequest</SendbackUrl>
  <EGIService>http://*************:8000</EGIService>
  <EGIUserNo>PCNDESIGN1_X</EGIUserNo>
  <EGIPsw>1</EGIPsw>
  <StationType>decorate</StationType>
</CADTaskClientConfig>