<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/cube2equirect_blur">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="world" type="Matrix4f" semantic="World" />
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj" />
  <Parameter name="tex_01" type="TextureCube" semantic="Texture0" />
  <!--x, blur_radius * sqrt(2); y, 1.0/radius = 2.0 / width; z, blur_radius = tan(blur_angle * 0.5f); max blur angle is 90 degree 0.0078125,0.17633-->
  <Parameter name="tex_size_info" type="Vector4f"/>
  <Sampler name="sampler_tex">
    <Texture value="tex_01" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP" />
    <AddressV value="WRAP" />
    <AddressW value="WRAP" />
  </Sampler>
  <Shader type="glsl">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			uniform mat4 world; 
			uniform mat4 vp;
			uniform vec4 tex_size_info;
 	
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 

			varying vec2 tc_tex;

			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
				tc_tex     = (a_uv0.xy + vec2(-0.25, 0)) * vec2(6.2831852, 3.1415926);			
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
      <![CDATA[
			precision mediump float;
		
			uniform vec4 tex_size_info;	
			uniform samplerCube sampler_tex;

			varying vec2 tc_tex;
      
			void main()
			{ 
        vec3 normal = vec3(0.0, 0.0, 0.0);
        normal.y = cos(tc_tex.y);
        float r = sqrt(1.0 - normal.y * normal.y);
        normal.x = r * cos(tc_tex.x);
        normal.z = r * sin(tc_tex.x);
        normal = normalize(normal);
        
        //binnormal:y, normal:z, tangent:x
        vec3 binnormal = abs(normal.y) > 0.9 ? vec3(1.0, 0.0, 0.0):vec3(0.0, 1.0, 0.0);
        vec3 tangent = cross(normal, binnormal);
        binnormal = cross(normal, tangent);
        
        mat4 tangent2world = mat4(tangent.x, tangent.y, tangent.z, 0.0,
                              binnormal.x, binnormal.y, binnormal.z, 0.0, 
                              normal.x, normal.y, normal.z, 0.0,
                              normal.x, normal.y, normal.z, 1.0);  
                              
       
        vec4 color_acc = textureCube(sampler_tex, normal);
        color_acc.w = 1.0;
        vec3 local_pt = vec3(0.0, 0.0, 0.0);
        vec2 temp_val = vec2(tex_size_info.z, tex_size_info.z);
        temp_val.x = length(temp_val);
        for(local_pt.x = -tex_size_info.z; local_pt.x  < tex_size_info.z; local_pt.x  += tex_size_info.y)
        {
          for(local_pt.y = -tex_size_info.z; local_pt.y < tex_size_info.z; local_pt.y  += tex_size_info.y)
          {
            vec3 world_normal = normalize((tangent2world * vec4(local_pt, 1.0)).xyz);
            vec4 tempclr = textureCube(sampler_tex, world_normal);
            temp_val.y = (temp_val.x - length(vec2( local_pt.x, local_pt.y))) / tex_size_info.y;
            color_acc += vec4(tempclr.xyz, 1.0) * temp_val.y;
          }
        }

				gl_FragColor = color_acc / color_acc.w;
        
			}
		]]></GLShader>
  </Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};
			layout (std140) uniform material
			{
				vec4 tex_size_info;
			};
		
			in vec4 a_position; 
			in vec2 a_uv0; 
		
  		out vec2 tc_tex;
	
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
				tc_tex     = (a_uv0.xy + vec2(-0.25, 0)) * vec2(6.2831852, 3.1415926);			
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
        <![CDATA[
			precision mediump float;
			layout (std140) uniform material
			{
				vec4 tex_size_info;
			};
		
			uniform samplerCube sampler_tex;
	
			in vec2 tc_tex;
			out vec4 fragColor;
	
			void main()
			{ 
        vec3 normal = vec3(0.0, 0.0, 0.0);
        normal.y = cos(tc_tex.y);
        float r = sqrt(1.0 - normal.y * normal.y);
        normal.x = r * cos(tc_tex.x);
        normal.z = r * sin(tc_tex.x);
        normal = normalize(normal);
        
        //binnormal:y, normal:z, tangent:x
        vec3 binnormal = abs(normal.y) > 0.9 ? vec3(1.0, 0.0, 0.0):vec3(0.0, 1.0, 0.0);
        vec3 tangent = cross(normal, binnormal);
        binnormal = cross(normal, tangent);
        
        mat4 tangent2world = mat4(tangent.x, tangent.y, tangent.z, 0.0,
                              binnormal.x, binnormal.y, binnormal.z, 0.0, 
                              normal.x, normal.y, normal.z, 0.0,
                              normal.x, normal.y, normal.z, 1.0);  
                              
       
        vec4 color_acc = textureCube(sampler_tex, normal);
        color_acc.w = 1.0;
        vec3 local_pt = vec3(0.0, 0.0, 0.0);
        vec2 temp_val = vec2(tex_size_info.z, tex_size_info.z);
        temp_val.x = length(temp_val);
        for(local_pt.x = -tex_size_info.z; local_pt.x  < tex_size_info.z; local_pt.x  += tex_size_info.y)
        {
          for(local_pt.y = -tex_size_info.z; local_pt.y < tex_size_info.z; local_pt.y  += tex_size_info.y)
          {
            vec3 world_normal = normalize((tangent2world * vec4(local_pt, 1.0)).xyz);
            vec4 tempclr = texture(sampler_tex, world_normal);
            temp_val.y = (temp_val.x - length(vec2( local_pt.x, local_pt.y))) / tex_size_info.y;
            color_acc += vec4(tempclr.xyz, 1.0) * temp_val.y;
          }
        }

				fragColor = color_acc / color_acc.w;;
			}
		]]></GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h" /><![CDATA[
cbuffer cbEnt : register(b0)
{
    matrix world;
};
cbuffer cbView : register(b1)
{
    matrix vp;
};
cbuffer cbMat : register(b2)
{
  float4 tex_size_info;
};
struct VSInput
{
    float3 pos : POSITION;
    float2 uv : TEXCOORD;
};

struct VSOutput
{
    float4 pos:SV_POSITION;
    float2 uv: TEXCOORD0;
};

SamplerState sampler_tex    : register( s0 );
TextureCube tex_01      : register( t0 );

VSOutput VsMain(VSInput input)
{
    VSOutput o;
    o.pos = mul(float4(input.pos, 1), mul(world, vp));
    o.uv = (input.uv - float2(0.25, 0.0f)) * float2(6.2831852f, 3.1415926f);
    return o;
}

float4 PsMain(VSOutput input) : SV_TARGET
{
    float3 normal = float3(0, 0, 0);
    normal.y = cos(input.uv.y);
    float r = sqrt(1 - normal.y * normal.y);
    normal.x = r * cos(input.uv.x);
    normal.z = r * sin(input.uv.x);
    normal = normalize(normal);
    
    //binnormal:y, normal:z, tangent:x
    float3 binnormal = abs(normal.y) > 0.9 ? float3(1,0,0):float3(0,1,0);
    float3 tangent = cross(normal, binnormal);
    binnormal = cross(normal, tangent);
    
    float4x4 tangent2world = float4x4(tangent.x, tangent.y, tangent.z, 0,
                          binnormal.x, binnormal.y, binnormal.z, 0, 
                          normal.x, normal.y, normal.z, 0,
                          normal.x, normal.y, normal.z, 1);  
                          
    //float4 color_acc = tex_01.Sample(sampler_tex, normal);
    //color_acc.w = 1;
    //float3 local_pt = float3(0.0, 0.0, 0.0);
    //for(local_pt.x = -tex_size_info.z; local_pt.x  < tex_size_info.z; local_pt.x  += tex_size_info.y)
    //{
    //  for(local_pt.y = -tex_size_info.z; local_pt.y < tex_size_info.z; local_pt.y  += tex_size_info.y)
    //  {
    //    float3 world_normal = normalize(mul(tangent2world, float4(local_pt, 1)).xyz);
    //    float4 tempclr = tex_01.Sample(sampler_tex, world_normal);
    //    tempclr.w = 1;
    //    color_acc += tempclr;
    //  }
    //}
    
    float4 color_acc = tex_01.Sample(sampler_tex, normal);
    color_acc.w = 1;
    float3 local_pt = float3(0.0, 0.0, 0.0);
    float2 temp_val = float2(tex_size_info.z, tex_size_info.z);
    temp_val.x = length(temp_val);
    for(local_pt.x = -tex_size_info.z; local_pt.x  < tex_size_info.z; local_pt.x  += tex_size_info.y)
    {
      for(local_pt.y = -tex_size_info.z; local_pt.y < tex_size_info.z; local_pt.y  += tex_size_info.y)
      {
        float3 world_normal = normalize(mul( float4(local_pt, 1),tangent2world).xyz);
        float4 tempclr = tex_01.Sample(sampler_tex, world_normal);
        temp_val.y = (temp_val.x - length(float2( local_pt.x, local_pt.y))) / tex_size_info.y;
        color_acc += float4(tempclr.xyz, 1) * temp_val.y;
      }
    }
    return color_acc / color_acc.w;
}
		]]></Shader>
  <Technique name="cube2equirect_blur">
    <Pass name="pass" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
</Effect>
