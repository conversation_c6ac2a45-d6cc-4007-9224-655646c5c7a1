precision highp float;
#define TRANSFORM_TEXCOORD(tex,trans) (tex.xy * trans.xy + trans.zw)
#define ROTATE_TEXCOORD(tex,trans) (vec2(dot(tex.xy, trans.xy), dot(tex.xy, trans.zw)))

#define MUL(w, vp) (w * vp)

#define lerp(a, b, t) (a + t * (b - a))

#define RECIPROCAL_PI2 0.15915494
#define RECIPROCAL_PI 0.31830988618

#if  __VERSION__ >= 300
#define SAMPLE_2D(s, t) texture(s, t)
#define SAMPLE_CUBE(s, t) texture(s, t)
#define SAMPLE_CUBE_LOD(s, t, l) textureLod(s, t, l)
#else
#define SAMPLE_2D(s, t) texture2D(s, t)
#define SAMPLE_CUBE(s, t) textureCube(s, t)
#define SAMPLE_CUBE_LOD(s, t, l) textureCube(s, t)
#endif

uniform vec4 scr_uv_modifier;

vec4 PlanarMapping(vec4 world_pos, mat4 ref_inv_world_mat)
{
	vec4 pos = world_pos * ref_inv_world_mat;
	pos /= pos.w;
	return pos * vec4(1.0, -1.0, 0.0, 0.0) + vec4(0.5, 0.5, 0.0, 0.0);
}

vec4 ScreenMapping(vec4 wvp_pos)
{
	wvp_pos /= wvp_pos.w;
	return wvp_pos * vec4(scr_uv_modifier.x, scr_uv_modifier.y, 0.0, 0.0) + vec4(0.5, 0.5, 0.0, 0.0);
}

vec4 ComputeReflectUVInVS(vec4 setting, mat4 world, mat4 v, mat4 tp,
	vec4 input_pos, vec2 input_tc, vec4 vec_ref)
{
	vec4 tc_ref;
	if (setting.x > 0.0)
	{
		if (setting.y > 0.0)
		{
			mat4 wvp1 = MUL(MUL(world, v), tp);
			tc_ref = input_pos * wvp1;
		}
		else
		{
			tc_ref.xy = TRANSFORM_TEXCOORD(input_tc, vec_ref);
		}
	}
	else
	{
		tc_ref.xy = TRANSFORM_TEXCOORD(input_tc, vec_ref);
		tc_ref.x = 1.0 - tc_ref.x;
	}

	return tc_ref;
}

vec4 ComputeReflectColorInPS(sampler2D sampler_ref, vec4 tc_ref, vec4 setting)
{
	vec4 col_ref;
	if (setting.y > 0.0)
	{
		vec4 r1 = ScreenMapping(tc_ref);
		col_ref = SAMPLE_2D(sampler_ref, r1.xy);
	}
	else
	{
		col_ref = SAMPLE_2D(sampler_ref, tc_ref.xy);
	}
	return col_ref;
}

#define PS_COMPUTE_CLIP  if(tc_tex.z > 0.0 && tc_tex.w < 0.0)  { discard; }

void ComputeClipInVS(inout vec4 tc_tex, vec4 input_pos, vec4 setting, float is_clip, vec4 clip_plane, mat4 world)
{
	tc_tex.z = 0.0;
	if (setting.x < 0.0 && is_clip > 0.0)
	{
		tc_tex.z = 1.0;
		vec4 world_pos = input_pos * world;
		tc_tex.w = dot(world_pos, clip_plane);
	}
}

vec2 ConvertCubeReflectVector2EquirectangleUV(vec3 reflect)
{
	vec2 ret;
	ret.y = acos(reflect.y);
	ret.x = atan(reflect.z, reflect.x);
	ret.xy = ret.xy * vec2(RECIPROCAL_PI2, RECIPROCAL_PI) + vec2(0.25, 0.0);
	return ret;
}

void ComputeProbeBoxWorldInfo(
	//three out parameter 
	out vec4 cwp_box_pos, out vec3 ref_normal, out vec4 ref_pos,
	//six in parameter
	mat4 world, mat4 inv_world_transpose, mat4 to_box_world_trans,
	vec4 local_pos, vec3 cam_pos_world, vec3 normal)
{
	//trasform vertex position from model space to box space via world space
	ref_pos = local_pos * world;
	ref_pos = ref_pos * to_box_world_trans;

	//trasform camera position from world space to box space
	cwp_box_pos = vec4(cam_pos_world, 1.0) * to_box_world_trans;

	//transform vertex normal from model space to box space via world space
	ref_normal = normal * mat3(inv_world_transpose);
	ref_normal = ref_normal * mat3(to_box_world_trans);
	ref_normal = normalize(ref_normal);
}

vec4 EquirectEnvMapping(sampler2D s, vec3 vertexPos, vec3 origUV, vec4 cubeMin, vec4 cubeMax, vec4 cubePos)
{
	if (cubePos.w > 0.0)
	{
		vec3 invOrigUV = 1.0 / origUV;
		vec3 intersecAtMaxPlane = (cubeMax.xyz - vertexPos) * invOrigUV;
		vec3 intersecAtMinPlane = (cubeMin.xyz - vertexPos) * invOrigUV;
		// Get the largest intersection values (we are not intersted in negative values)
		vec3 largestIntersec = max(intersecAtMaxPlane, intersecAtMinPlane);
		// Get the closest of all solutions
		float distance = min(min(largestIntersec.x, largestIntersec.y), largestIntersec.z);
		// Get the intersection position
		vec3 intersectPositionWS = vertexPos + origUV * distance;
		// Get corrected vector
		intersectPositionWS = normalize(intersectPositionWS - cubePos.xyz);

		vec2 uv = ConvertCubeReflectVector2EquirectangleUV(intersectPositionWS);
		return SAMPLE_2D(s, uv.xy);
	}
	else
	{
		vec2 uv = ConvertCubeReflectVector2EquirectangleUV(origUV);
		return SAMPLE_2D(s, uv.xy);
	}
}

vec4 EnvMapping(samplerCube s, vec3 vertexPos, vec3 origUV, vec4 cubeMin, vec4 cubeMax, vec4 cubePos)
{
	// Find the ray intersection with box plane
	if (cubePos.w > 0.0)
	{
		vec3 invOrigUV = 1.0 / origUV;
		vec3 intersecAtMaxPlane = (cubeMax.xyz - vertexPos) * invOrigUV;
		vec3 intersecAtMinPlane = (cubeMin.xyz - vertexPos) * invOrigUV;
		// Get the largest intersection values (we are not intersted in negative values)
		vec3 largestIntersec = max(intersecAtMaxPlane, intersecAtMinPlane);
		// Get the closest of all solutions
		float distance = min(min(largestIntersec.x, largestIntersec.y), largestIntersec.z);
		// Get the intersection position
		vec3 intersectPositionWS = vertexPos + origUV * distance;
		// Get corrected vector
		intersectPositionWS = normalize(intersectPositionWS - cubePos.xyz);

		return SAMPLE_CUBE_LOD(s, intersectPositionWS, 0.0);
	}
	else
	{
		return SAMPLE_CUBE(s, origUV);
	}
}

vec3 reflectProbeCorrectNormal(vec3 vertexPos, vec3 origUV, vec3 cubeMin, vec3 cubeMax, vec3 cubePos)
{
	// Find the ray intersection with box plane
	vec3 invOrigUV = vec3(1.0, 1.0, 1.0) / origUV;
	vec3 intersecAtMaxPlane = (cubeMax - vertexPos) * invOrigUV;
	vec3 intersecAtMinPlane = (cubeMin - vertexPos) * invOrigUV;
	// Get the largest intersection values (we are not intersted in negative values)
	vec3 largestIntersec = max(intersecAtMaxPlane, intersecAtMinPlane);
	// Get the closest of all solutions
	float distance = min(min(largestIntersec.x, largestIntersec.y), largestIntersec.z);
	// Get the intersection position
	vec3 intersectPositionWS = vertexPos + origUV * distance;
	// Get corrected vector
	return intersectPositionWS - cubePos;
}



