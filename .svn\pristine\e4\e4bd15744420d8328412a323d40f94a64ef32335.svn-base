﻿<?xml version="1.0" encoding="utf-8" ?>
<Root device_type="D3D11">
  <!--<IsLoadVr>true</IsLoadVr>
  <IsEncryptScene>true</IsEncryptScene>
  <IsSceneDll>false</IsSceneDll>
  <RenderLargeCameraName>cam_Normal</RenderLargeCameraName>
  <SceneFilePath>Model\Hall\decoration_test0901.zxrm</SceneFilePath>
  <HallModel>Hall.zxvm</HallModel>
  <IsShowMaskDialog>true</IsShowMaskDialog>
  <TextureDirList>
    <SceneFilePath>Textures</SceneFilePath>
    <SceneFilePath>Textures\AppDefine</SceneFilePath>
  </TextureDirList>
  <ModelDirList>
    <SceneFilePath>Model\</SceneFilePath>
  </ModelDirList>
  <SoundDirList>
    <SceneFilePath Absolute="true">E:\KCD_R12.1\bin\Release\sound</SceneFilePath>
  </SoundDirList>
  <ContentNameList>
    <Item>Global_Select</Item>
    <Item>Global_Hwndmsg</Item>
    <Item>Global_Setting_Car</Item>
    <Item>Global_Setting_Hall</Item>
    <Item>Global_Setting_Parameter</Item>
  </ContentNameList>
  <PartAffect>-->
    <!--
  由于某个部件变化后，其他部件也必须在vr中进行刷新，
  此处给出受影响的部件，直接给出部件名称即可
  同时每个部件给出优先级，即变化优先级。
  -->
    <Top>
      <Priority>1000</Priority>
      <ChangeTogether>
        <LeftWall/>
        <BackWall/>
        <RightWall/>
      </ChangeTogether>
    </Top>
    <CarConfig>
      <Priority>1000</Priority>
      <ChangeTogether>
        <Top/>
        <Cop/>
        <CopDisplay/>
        <FrontWall/>
        <CarDoor/>
        <Mirror/>
        <Bottom/>
        <LeftWall/>
        <BackWall/>
        <RightWall/>
        <Handrail/>
        <CarShell/>
		<LeftAccessory/>
		<BackAccessory/>
		<RightAccessory/>
		<BottomAccessory/>
        <SidePanelProtection/>
        <CarSecurityWindow/>
        <BackProtectiveWall/>
        <LeftProtectiveWall/>
        <RightProtectiveWall/>
        <CarDoorTrim/>
        <!--<LoadingSightseeing/>-->
        <!--<EMIDS/>-->
      </ChangeTogether>
    </CarConfig>
	<Bottom>
      <ChangeTogether>
        <BottomAccessory/>
      </ChangeTogether>
    </Bottom>
    <Cop>
      <ChangeTogether>
        <CopDisplay/>
        <FrontWall/>
      </ChangeTogether>
    </Cop>
    <CopWall>
      <VrArrayPartType>FrontWall</VrArrayPartType>
    </CopWall>
    <DoorHeader>
      <VrArrayPartType>FrontWall</VrArrayPartType>
    </DoorHeader>
	<!--此处轿门变化联动外壳变化是因为西奥轿门材质变化导致外壳门变化-->
	<CarDoor>
	  <ChangeTogether>
        <CarShell/>
      </ChangeTogether>
    </CarDoor>
    <!--<EMIDS>
      <ChangeTogether>
        <EMIDSDisplay/>
      </ChangeTogether>      
    </EMIDS>-->
    <HDCop>
      <ChangeTogether>
        <CopDisplay/>
      </ChangeTogether>
      <VrArrayPartType>Cop</VrArrayPartType>
    </HDCop>
    <AuxCop>
      <VrArrayPartType>Cop</VrArrayPartType>
    </AuxCop>
  <LeftProtectiveWall>
      <ChangeTogether>
        <BackProtectiveWall/>
        <RightProtectiveWall/>
      </ChangeTogether>
    </LeftProtectiveWall>
   <HallConfig>
      <ChangeTogether>
        <!--<Hall/>-->
        <Jamb/>
        <Lop/>
        <LopDisplay/>
        <HallDoor/>
        <Lantern/>
        <LanternDisplay/>
        <HallIndicator/>
        <HallIndicatorDisplay/>
        <HallWall/>
        <HallSecurityWindow/>
        <HallFloor/>
        <HallShaft/>
        <HallDisplay/>
        <HallDoorTrim/>
		<HallFireBox/>
      </ChangeTogether>
    </HallConfig>
    <Lop>
      <ChangeTogether>
        <LopDisplay/>
      </ChangeTogether>
    </Lop>
    <HallIndicator>
      <ChangeTogether>
        <HallIndicatorDisplay/>
      </ChangeTogether>
    </HallIndicator>
    <Lantern>
      <ChangeTogether>
        <LanternDisplay/>
      </ChangeTogether>
    </Lantern>
  <!--</PartAffect>-->
  <!--IgnoreVisitor主要用于调试vr时跳过某些有错误的vrvisitor
  <IgnoreVisitor>
    <Top/>
    <Cop/>
    <CopDisplay/>
  </IgnoreVisitor>-->
</Root>
