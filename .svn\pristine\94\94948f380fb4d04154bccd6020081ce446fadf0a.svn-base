﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, minimal-ui">
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />

    <title>2130</title>

    <style type="text/css">
        /*
          disable rubber band
          https://gist.github.com/Subash/fe82b0c1ba53347a2399
        */
        html {
            height: 100%;
            width: 100%;
            overflow: hidden;
            touch-action: none;
            user-select: none;
        }

        body {
            height: 100%;
            padding: 0;
            overflow: auto;
            margin: 0 auto;
            -webkit-overflow-scrolling: touch;
            font-family: sans-serif;
            touch-action: none;
            user-select: none;
        }

        /*h1 {
            font-weight: 100;
            color: #666;
            text-align: center;
        }

        pre.code {
            white-space: pre-wrap;
        }*/

        .vue-pano,#app {
            margin: 0;
            touch-action: none;
            user-select: none;
            padding: 0;
        }
        /*style="padding: 0px; margin: 0px;touch-action: none; width: 100vw; height: 100vh;"*/

        /*@media screen and (max-width: 720px) {
            body > * {
                display: none;
            }

            body > #app {
                display: block;
            }

            .vue-pano {
                margin: 0;
                touch-action: none;
                padding: 0;
            }
        }*/

        .viewport {
            font-family: Helvetica, Arial, sans-serif;
            position: relative;
            -webkit-touch-callout: none; /* iOS Safari */
            -webkit-user-select: none; /* Safari */
            -khtml-user-select: none; /* Konqueror HTML */
            -moz-user-select: none; /* Firefox */
            -ms-user-select: none; /* Internet Explorer/Edge */
            user-select: none; /* Non-prefixed version, currently supported by Chrome and Opera */
            touch-action:none;
            cursor: -webkit-grab;
            width:100vw;
            height:100vh;
        }

        .viewport.dragging {
            cursor: -webkit-grabbing;
        }

        .viewport > * {
            position: absolute;
        }

        .title {
            color: #fff;
            font-size: 20px;
            font-weight: 300;
            margin: 0;
            right: 10px;
            top: 10px;
        }

        canvas {
            background: #000;
            touch-action: none;
            user-select: none;
            left: 0;
            top: 0;
            z-index: -1;
        }
    </style>
</head>

<body>
    <script src="../<EMAIL>"></script>
    <script src="../math.js"></script>
    <script type="text/javascript" src="../preventoverscroll.min.js"></script>
    <!--<script src="https://cdn.jsdelivr.net/npm/vue@2.5.16/dist/vue.js"></script> :class="{ dragging: dragging }" -->
    <div class="vue-pano" id="panocontainer">
        <div id="app"
             class="vue-pano viewport" ref="viewport" 
             @mousedown="startDrag" @touchstart.stop="startDrag"
             @mousemove="onDrag" @touchmove.stop="onDrag"
             @mouseup="stopDrag" @touchend.stop="stopDrag" @mouseleave="stopDrag">
            <canvas id="canvas3d" ref="canvas" />
        </div>
    </div>
    <script>
        function getProps()
        {
            return {
                bundle: "", //example/assets/pano/hall-of-fame/
                format: "jpg",
            };
        }
    </script>
    <script src="../panorma_viewer.js"></script>
</body>
</html>
