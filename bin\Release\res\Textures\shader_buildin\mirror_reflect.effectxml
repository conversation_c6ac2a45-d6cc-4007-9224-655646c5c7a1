﻿<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/mirror_reflect">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj" />
  <Parameter name="world" type="Matrix4f" semantic="World" />
  <Parameter name="tex_lm" type="Texture2D" semantic="Texture0" />
  <Parameter name="tex_channel" type="Texture2D" semantic="Texture1" />
  <Parameter name="vec_channel" type="Vector4f" default="1,1,0,0" />
  <Parameter name="vec_ref" type="Vector4f" default="1,1,0,0" />
  <Parameter name="plate_emissive" type="Colorf" default="1,1,1,1" />
  <Parameter name="is_clip" type="float32" default="0" semantic="EffectGlobal" />
  <Parameter name="clip_plane" type="Vector4f" default="0,1,0,0" semantic="EffectGlobal" />
  <Sampler name="s_lm">
    <Texture value="tex_lm" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="CLAMP" />
    <AddressV value="CLAMP" />
    <AddressW value="CLAMP" />
  </Sampler>
  <Sampler name="s_channel">
    <Texture value="tex_channel" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP" />
    <AddressV value="WRAP" />
    <AddressW value="WRAP" />
  </Sampler>
  <Shader type="glsl">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[		
			uniform mat4 vp;
			uniform mat4 world;
			//uniform mat4 wv;
			uniform vec4 vec_channel;   
			
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 
		
			varying vec4 tc_lm;
			varying vec2 tc_channel;
			
			uniform vec4 vec_ref;
			uniform mediump float is_clip;
			uniform vec4 clip_plane;
			
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;	
						
				tc_lm.xy = a_uv0.xy;	
				tc_channel = a_uv0.xy;
				vec4 channel_k  = vec4(tc_channel.x, tc_channel.y, 0.0, 1.0);
				vec2 channel_l  = TRANSFORM_TEXCOORD(channel_k, vec_channel);	
				tc_channel.xy  = channel_l;	
				
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					tc_lm.w = dot(world_pos, clip_plane);
				}
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel"><![CDATA[
			precision mediump float;
			
			uniform sampler2D s_lm;
			uniform sampler2D s_channel;
	
			uniform vec4 plate_emissive;
			  
			varying vec4 tc_lm;
			varying vec2 tc_channel;
			
			uniform mediump float is_clip;
			uniform vec4 clip_plane;
	
			void main()
			{ 
				if(is_clip > 0.0 && tc_lm.w < 0.0)  { discard; }
				
				vec4 col_lm = texture2D(s_lm, tc_lm.xy) ;
				vec4 col_channel = texture2D(s_channel, tc_channel) ;

				vec4 col_a1 = col_lm * plate_emissive ; 
				col_a1.w = col_channel.w;
				vec4 col_a2 = vec4(1.0, 1.0, 1.0, 1.0) * (1.0 - col_channel.w);

				gl_FragColor = col_a1 + col_a2;
			}
		]]></GLShader>
  </Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[		
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};
			layout (std140) uniform material
			{
				vec4 vec_channel;
				vec4 vec_ref;
				vec4 plate_emissive;
			};
			
			in vec4 a_position; 
			in vec2 a_uv0; 
		
			out vec4 tc_lm;
			out vec2 tc_channel;
			
			layout (std140) uniform global
			{
      vec4 clip_plane;
      mediump float is_clip;
			};
			
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;	
						
				tc_lm.xy = a_uv0.xy;	
				tc_channel = a_uv0.xy;
				vec4 channel_k  = vec4(tc_channel.x, tc_channel.y, 0.0, 1.0);
				vec2 channel_l  = TRANSFORM_TEXCOORD(channel_k, vec_channel);	
				tc_channel.xy  = channel_l;	
				
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					tc_lm.w = dot(world_pos, clip_plane);
				}
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
    <![CDATA[
			precision mediump float;
			
			uniform sampler2D s_lm;
			uniform sampler2D s_channel;
	
			layout (std140) uniform material
			{
				vec4 vec_channel;
				vec4 vec_ref;
				vec4 plate_emissive;
			};
			  
			in vec4 tc_lm;
			in vec2 tc_channel;
			out vec4 fragColor;
			
			layout (std140) uniform global
			{
      vec4 clip_plane;
      mediump float is_clip;
			};
	
			void main()
			{ 
				if(is_clip > 0.0 && tc_lm.w < 0.0)  { discard; }
				
				vec4 col_lm = texture(s_lm, tc_lm.xy) ;
				vec4 col_channel = texture(s_channel, tc_channel) ;

				vec4 col_a1 = col_lm * plate_emissive ; 
				col_a1.w = col_channel.w;
				vec4 col_a2 = vec4(1.0, 1.0, 1.0, 1.0) * (1.0 - col_channel.w);

				fragColor = col_a1 + col_a2;
			}
		]]></GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h" /><![CDATA[
    cbuffer cbEntity : register( b0 )
    {
		matrix world;
    };
    cbuffer cbView : register( b3 )
    {
		matrix vp;
    };
	
    cbuffer cbMaterial : register( b1 )
    {
		float4 vec_channel;   
		float4 plate_emissive;
    };
	
    cbuffer cbGlobal : register( b2 )
    {
    float4 clip_plane;
    float is_clip;
    };
    
    SamplerState s_lm : register( s0 );
    Texture2D tex_lm : register( t0 );
	
    SamplerState s_channel : register( s1 );
    Texture2D tex_channel : register( t1 );
    
		struct VsInput
		{
			float3     pos:POSITION;
			float2     tc :TEXCOORD; 
		};
    
		struct VsOutput
		{
			float4   pos             :   SV_POSITION ;
			float4   tc_lm           :   TEXCOORD0;
			float2   tc_channel      :   TEXCOORD2;	
		};
    
		VsOutput VsMain(VsInput input)
		{
			VsOutput o = (VsOutput)0;
		
			float4   r0=mul(float4(input.pos,1),MUL(world, vp));
			o.pos=r0;			
			o.tc_lm.xy = input.tc;	
			o.tc_channel  = input.tc;
			float4 channel_k  = float4(o.tc_channel.x, o.tc_channel.y, 0, 1);
			float2 channel_l  = TRANSFORM_TEXCOORD(channel_k, vec_channel);	
			o.tc_channel.xy  = channel_l;	


			if(is_clip > 0)
			{
				float4 world_pos = mul(float4(input.pos, 1), world);
				o.tc_lm.w = dot(world_pos, clip_plane);
			}
			return o;
		}
    
		float4 PsMain(VsOutput input) : SV_Target
		{
			if(is_clip > 0 ) { clip(input.tc_lm.w);}
			float4 col_lm       = tex_lm.Sample( s_lm,input.tc_lm.xy) ;
			float4 col_channel  = tex_channel.Sample( s_channel,input.tc_channel) ;

			float4 col_a1       = col_lm * plate_emissive ; 
					col_a1.w     = col_channel.w;
			float4 col_a2       =  1-col_channel.w;

			return col_a1+col_a2;
		}
		]]></Shader>
  <Technique name="tech" shadertype="hlsl" devicetype="dx9">
    <Pass name="pass" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
      <State name="AlphaBlendEnable" value="true" />
      <State name="SrcBlend" value="ZERO" />
      <State name="DestBlend" value="SRCCOLOR" />
      <State name="DepthBias" value="-1" />
    </Pass>
  </Technique>
</Effect>