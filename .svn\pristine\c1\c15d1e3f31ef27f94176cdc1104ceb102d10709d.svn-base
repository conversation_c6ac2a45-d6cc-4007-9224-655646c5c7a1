﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PicWorkStationWindowsFormsForALi
{
    class PanoramaTaskProcess : DefaultTaskProcess
    {
        public PanoramaTaskProcess(ITaskServer taskServer) : base(taskServer)
        {            
        }
        public override bool IsCanProcess(TaskInfo info)
        {
            return info.TaskType == Const.TaskType_PanoramaTask;
        }

        public override void DoProcessPreRunTask(TaskInfo info, object param)
        {
            if (info.OutFiles == null) info.OutFiles = new List<object>();
            info.OutFiles.Clear();

            info.OutFiles.AddRange(Const.PanoramaImages);
        }
        public override bool DoProcessAfterEndTask(TaskInfo taskInfo, object param)
        {
            try
            {
                var srcHtml = AppDomain.CurrentDomain.BaseDirectory + "res\\ReportTemplate\\pano.html";
                File.Copy(srcHtml, Path.Combine(taskInfo.WorkPath, "pano.html"));

                TaskServer.WriteLog(Const.KEY_START_COPY_FILE_TO_SHARED_FOLDERS, Const.MSG_TYPE_Normal);

                var path = Path.Combine(taskInfo.SharedFolder, taskInfo.NewFileDir);
                CreateDirectory(path);

                if (IsZipCompress(taskInfo))
                {
                    if (Path.DirectorySeparatorChar != path[path.Length - 1]) path += Path.DirectorySeparatorChar;

                    string zipFileToCreate = path + "output.zip";
                    string directoryToZip = taskInfo.WorkPath;
                    CreateZipFile(zipFileToCreate, directoryToZip);
                }
                else
                {
                    TaskServer.WriteLog("It's not zip compress.", Const.MSG_TYPE_Normal);
                    Helper.CopyDirectory(taskInfo.WorkPath, path);
                }
                TaskServer.WriteLog(Const.KEY_END_COPY_FILE_TO_SHARED_FOLDERS, Const.MSG_TYPE_Normal);

                Directory.Delete(taskInfo.WorkPath, true);
                string workPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, taskInfo.NewFileDir);
                if(Directory.Exists(workPath)) Directory.Delete(workPath, true);

                return true;
            }
            catch (Exception ex)
            {
                TaskServer.WriteLog(string.Format(Const.KEY_COPY_FILE_EXCEPTION, ex), Const.MSG_TYPE_ERROR);

                return false;
            }
        }

        public override void DoProcessByMultilayer(int multilayer, TaskInfo info, object param)
        {

        }
    }
}
