<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/texture_planar_mapping">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj"/>
  <Parameter name="ref_inv_world_mat" type="Matrix4f" />
  <Parameter name="tex_01" type="Texture2D" semantic="Texture0"/>
  <Parameter name="world" type="Matrix4f" semantic="World"/>
  <Parameter name="is_clip" type="float32" default="0" semantic="EffectGlobal"/>
  <Parameter name="clip_plane" type="Vector4f" default="0,1,0,0" semantic="EffectGlobal"/>
  <Sampler name="s_tex">
    <Texture value="tex_01"/>
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR"/>
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP"/>
    <AddressV value="WRAP"/>
    <AddressW value="WRAP"/>
  </Sampler>
	<Shader type="glsl">
		<Include file="shader_buildin/gl_inc.h"/>
		<GLShader name="VsMain" type="Vertex">
			<![CDATA[
			uniform mat4 vp;
			uniform mat4 ref_inv_world_mat;  
			uniform mat4 world;  
			
			uniform vec4 clip_plane;
			uniform mediump float is_clip;
			
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 
		
			varying vec4 tex_uv; 
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				vec4 world_pos = a_position * world;
				tex_uv = PlanarMapping(world_pos, ref_inv_world_mat);
 
				if(is_clip > 0.0)
				{
					tex_uv.w = dot(world_pos, clip_plane);
				}
				else
				{
					tex_uv.w = 1.0;
				}
			}
			]]>
		</GLShader>
		<GLShader name="PsMain" type="Pixel">
			<![CDATA[
			precision mediump float;
			
			uniform vec4 clip_plane;
			uniform mediump float is_clip;
			
			uniform sampler2D s_tex;
	
			varying vec4 tex_uv; 
	
			void main()
			{ 
				if(tex_uv.w < 0.0)  { discard; }
     
				gl_FragColor = texture2D(s_tex, tex_uv.xy);
			}
		]]>
		</GLShader>
	</Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h"/>
    <GLShader name="VsMain" type="Vertex">
      <![CDATA[
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};
			layout (std140) uniform material
			{
				mat4 ref_inv_world_mat;
			};
			layout (std140) uniform global
			{
				vec4 clip_plane;
				mediump float is_clip;
			};
			
			in vec4 a_position; 
			in vec2 a_uv0; 
		
			out vec4 tex_uv; 
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				vec4 world_pos = a_position * world;
				tex_uv = PlanarMapping(world_pos, ref_inv_world_mat);
 
				if(is_clip > 0.0)
				{
					tex_uv.w = dot(world_pos, clip_plane);
				}
				else
				{
					tex_uv.w = 1.0;
				}
			}
			]]>
    </GLShader>
    <GLShader name="PsMain" type="Pixel">
      <![CDATA[
			precision mediump float;
			
			layout (std140) uniform global
			{
				vec4 clip_plane;
				mediump float is_clip;
			};
			
			uniform sampler2D s_tex;
	
			in vec4 tex_uv; 
			out vec4 fragColor;
	
			void main()
			{ 
				if(tex_uv.w < 0.0)  { discard; }
     
				fragColor = texture(s_tex, tex_uv.xy);
			}
		]]>
    </GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h"/>
    <![CDATA[
    cbuffer cbEntity : register( b0 )
    {
		matrix world;
    };
    cbuffer cbView : register( b3 )
    {
    matrix vp;
    };
    cbuffer cbMaterial : register( b1 )
    {
    matrix ref_inv_world_mat;  
    };
    
    cbuffer cbGlobal : register( b2 )
    {
    float4 clip_plane;
    float is_clip;
    };

    
		SamplerState s_tex : register( s0 );
    Texture2D tex_01 : register( t0 );
    
    struct VsInput
    {
      float3 pos : POSITION;
    };
    
    struct VsOutput
    {
      float4 pos : SV_POSITION;   
      float4 tex_uv : TEXCOORD0;
    };
    
    VsOutput VsMain(VsInput input)
    {
	    VsOutput o = (VsOutput)0;
	    o.pos = mul(float4(input.pos, 1), MUL(world, vp));
      float4 world_pos = mul(float4(input.pos, 1), world);
      o.tex_uv.xy = PlanarMapping(world_pos, ref_inv_world_mat);
 
      if(is_clip > 0)
      {
        o.tex_uv.w = dot(world_pos, clip_plane);
      }
      else
      {
        o.tex_uv.w = 1;
      }
	    return o;
    }
    
    float4 PsMain(VsOutput input) : SV_Target
    {
      if(is_clip > 0)  { clip(input.tex_uv.w); }
     
      return tex_01.Sample(s_tex, input.tex_uv.xy);
    }
    ]]>
  </Shader>
  <Technique name="Emissive" shadertype="hlsl" devicetype="dx9">
    <Pass name="pass1" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0"/>
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0"/>
    </Pass>
  </Technique>
</Effect>
