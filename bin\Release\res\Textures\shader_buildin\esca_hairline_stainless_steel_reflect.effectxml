<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/esca/esca_hairline_stainless_steel_reflect">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="world" type="Matrix4f" semantic="World" />
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj" />
  <Parameter name="v" type="Matrix4f" semantic="View" />
  <Parameter name="tex0" type="Texture2D" semantic="Texture0" />
  <Parameter name="tp" type="Matrix4f" />
  <Parameter name="emissive" type="Colorf" default="1,1,1,1" />
  <Sampler name="s_ref">
    <Texture value="tex0" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP" />
    <AddressV value="WRAP" />
    <AddressW value="WRAP" />
  </Sampler>
  <Shader type="glsl">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			uniform mat4 world;
			uniform mat4 vp;
			uniform mat4 v;
			uniform mat4 tp;
		
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 
		
			varying vec4 tc_ref;
		
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
	
				mat4 wvp1 = MUL(world, v) * tp;
				tc_ref = a_position * wvp1;
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel"><![CDATA[
			precision mediump float;
		
			uniform sampler2D  s_ref;
      uniform vec4 emissive;		
			varying vec4 tc_ref;
		
			void main()
			{ 
				vec4 r1 = ScreenMapping(tc_ref);

				vec4 col_ref    = texture2D( s_ref,r1.xy );				
				gl_FragColor = col_ref * emissive;
			}
		]]></GLShader>
  </Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			layout (std140) uniform entity
			{
      mat4 world;
      };
      layout (std140) uniform view
      {
				mat4 v;
				mat4 vp;
			};
			layout (std140) uniform material
			{
				mat4 tp;
				vec4 emissive;
			};
		
			in vec4 a_position; 
			in vec2 a_uv0; 
		
			out vec4 tc_ref;
		
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
	
				mat4 wvp1 = MUL(world, v) * tp;
				tc_ref = a_position * wvp1;
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel"><![CDATA[
			precision mediump float;
		
			uniform sampler2D  s_ref;
			layout (std140) uniform material
			{
				mat4 tp;
				vec4 emissive;
			};
			in vec4 tc_ref;
			out vec4 fragColor;
		
			void main()
			{ 
				vec4 r1 = ScreenMapping(tc_ref);

				vec4 col_ref    = texture( s_ref,r1.xy );				
				fragColor = col_ref * emissive;
			}
		]]></GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h" /><![CDATA[
    cbuffer cbEntity : register( b0 )
    {
    matrix world;
    };
    cbuffer cbView : register( b2 )
    {
    matrix v;   
    matrix vp;
    };
    
    cbuffer cbMaterial : register( b1 )
    {
		matrix tp;
    float4 emissive;
    };
    
  	SamplerState s_ref : register( s0 );
    Texture2D tex0 : register( t0 );
	
		struct VsInput
		{
			float3     pos:POSITION;
			float2     tc :TEXCOORD; 
		};
    
		struct VsOutput
		{
			float4   pos             :   SV_POSITION ;
			float4   tc_ref          :   TEXCOORD0;
		};
    
		VsOutput VsMain(VsInput input)
		{
			VsOutput o = (VsOutput)0;
	
			float4   r0=mul(float4(input.pos,1),MUL(world, vp));
			o.pos=r0;
	
			float4x4 wvp1 = mul(MUL(world, v), tp);
			o.tc_ref     = mul(float4(input.pos,1),wvp1);

			return o;
		}
    
		float4 PsMain(VsOutput input) : SV_Target
		{
			float4 r1 = ScreenMapping(input.tc_ref);

			float4 col_ref    = tex0.Sample( s_ref,r1 ) ;			
			return  col_ref * emissive;
		}
		]]></Shader>
  <Technique name="tech">
    <Pass name="pass" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
</Effect>
