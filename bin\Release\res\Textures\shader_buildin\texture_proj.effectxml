<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/texture_proj">
  <!--必须有name,type；semantic，default, 可选-->
  <Parameter name="emissive" type="Colorf" default= "1,1,1,1"/>
  <Parameter name="tex01" type="Texture2D" semantic="Texture0"/>
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj"/>
  <Parameter name="v" type="Matrix4f" semantic="View"/>
  <Parameter name="tp" type="Matrix4f"/>
  <Parameter name="world" type="Matrix4f" semantic="World"/>
  <Parameter name="is_clip" type="float32" default="0" semantic="EffectGlobal"/>
  <Parameter name="clip_plane" type="Vector4f" default="0,1,0,0" semantic="EffectGlobal"/>
  <!--必须有Texture；其余可选-->
  <Sampler name="sampler01">
    <Texture value="tex01"/>
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR"/>
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP"/>
    <AddressV value="WRAP"/>
    <AddressW value="WRAP"/>
  </Sampler>
<Shader type="glsl">
		<Include file="shader_buildin/gl_inc.h"/>
		<GLShader name="VsMain" type="Vertex">
			<![CDATA[
			uniform mat4 vp;
			uniform mat4 world;   
			uniform mat4 tp;
			uniform mat4 v;
						
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 

			varying vec4 uv;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				mat4 mvp1= MUL(world, v) * tp;
				uv = a_position * mvp1;
			}
			]]>
		</GLShader>
		<GLShader name="PsMain" type="Pixel">
			<![CDATA[
			precision mediump float;
						
			uniform sampler2D sampler01;
			
			varying vec4 uv;

			uniform vec4 emissive;
			uniform vec4 clip_plane;
			uniform mediump float is_clip;
	
			void main()
			{ 
				if(is_clip > 0.0 && uv.w < 0.0)  { discard; }
        
				vec4 r1=ScreenMapping(uv);
		
				vec4 clr = texture2D(sampler01, r1.xy);
				gl_FragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a); //ModulateAlpha C = Cs*Cd, A = As*Ad

			}
		]]>
		</GLShader>
	</Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h"/>
    <GLShader name="VsMain" type="Vertex">
      <![CDATA[
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 v;
				mat4 vp;
			};
			layout (std140) uniform material
			{
				mat4 tp;
				vec4 emissive;
			};
						
			in vec4 a_position; 
			in vec2 a_uv0; 

			out vec4 uv;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				mat4 mvp1= MUL(world, v) * tp;
				uv = a_position * mvp1;
			}
			]]>
    </GLShader>
    <GLShader name="PsMain" type="Pixel">
      <![CDATA[
			precision mediump float;
						
			uniform sampler2D sampler01;
			
			in vec4 uv;
			out vec4 fragColor;

			layout (std140) uniform material
			{
				mat4 tp;
				vec4 emissive;
			};
			layout (std140) uniform global
			{
				vec4 clip_plane;
				mediump float is_clip;
			};
	
			void main()
			{ 
				if(is_clip > 0.0 && uv.w < 0.0)  { discard; }
        
				vec4 r1=ScreenMapping(uv);
		
				vec4 clr = texture(sampler01, r1.xy);
				fragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a); //ModulateAlpha C = Cs*Cd, A = As*Ad

			}
		]]>
    </GLShader>
  </Shader>
  <!--必须有type:hlsl;cg;glsl;目前实现了hlsl-->
<Shader type="hlsl5">
    <!--一个Shader可以有零个或多个Include，必须有file：指定文件名-->
    <Include file="shader_buildin/dx_inc.h"/>
    <![CDATA[
    cbuffer cbEntity : register( b0 )
    {
		matrix world;
    };
    cbuffer cbView : register( b3 )
    {
    matrix v;   
    matrix vp;
    };

    cbuffer cbMaterial : register( b1 )
    {
	  float4x4 tp;
    float4 emissive;    
    };
    
    cbuffer cbGlobal : register( b2 )
    {
    float4 clip_plane;
    float is_clip;
    };
    
    SamplerState sampler01 : register( s0 );
    Texture2D tex01 : register( t0 );

    struct VsInput
    {
      float3 pos : POSITION;
      float2 uv : TEXCOORD0;
    };

    struct VsOutput
    {
      float4 pos : SV_POSITION;
      float4 uv : TEXCOORD0;
    };
    
    VsOutput VsMain(VsInput input)
    {
		VsOutput o = (VsOutput)0;
		o.pos = mul(float4(input.pos, 1), MUL(world, vp));
		float4x4 mvp1= mul(MUL(world, v), tp);
		o.uv = mul(float4(input.pos,1),mvp1);

		return o;
    }
	
	float4 PsMain(VsOutput input) : SV_Target
    {
		if(is_clip > 0)  { clip(input.uv.w); }
    
		float4 r1=ScreenMapping(input.uv);

		float4 clr = tex01.Sample(sampler01, r1.xy);
		return float4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a); //ModulateAlpha C = Cs*Cd, A = As*Ad
    }
    ]]>
  </Shader>
  <Technique name="TextureDiffuse">
    <Pass name="pass0" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0"/>
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0"/>
    </Pass>
  </Technique>
</Effect>

