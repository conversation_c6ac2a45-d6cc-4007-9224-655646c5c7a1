<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/equirect_cube_reflect">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="world" type="Matrix4f" semantic="World" />
  <Parameter name="inv_world_transpose" type="Matrix4f" semantic="InverseWorldTranspose"/>
  <Parameter name="camera_world_pos" type="Vector3f" semantic="CameraWorldPosition"/>
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj" />
  <Parameter name="tex_01" type="Texture2D" semantic="Texture0" />
  <Sampler name="sampler_tex">
    <Texture value="tex_01" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP" />
    <AddressV value="WRAP" />
    <AddressW value="WRAP" />
  </Sampler>
  <Shader type="glsl">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			uniform mat4 world;
			uniform mat4 vp;
			uniform mat4 inv_world_transpose;
			uniform vec3 camera_world_pos;
		
			attribute vec4 a_position; 
			attribute vec2 a_uv0;
      attribute vec3 a_normal;
		
	    varying vec3 tc_normal;
      varying vec3 tc_pos;
      
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
	      tc_normal = a_normal * mat3(inv_world_transpose);
	      tc_normal = normalize(tc_normal);
        tc_pos = (a_position * world).xyz;
      }
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
        <![CDATA[
			precision mediump float;
		
			uniform sampler2D sampler_tex;
			uniform vec3 camera_world_pos;
      
	    varying vec3 tc_normal;
      varying vec4 tc_pos;
	
			void main()
			{ 
        vec3 uv = normalize(reflect(camera_world_pos - tc_pos.xyz, tc_normal));

        vec2 uv2d = ConvertCubeReflectVector2EquirectangleUV(uv);
        
				gl_FragColor = texture2D(sampler_tex, uv2d) ;
			}
		]]></GLShader>
  </Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			
			layout (std140) uniform entity
			{
				mat4 world;
				mat4 inv_world_transpose;
			};
			layout (std140) uniform view
			{
				mat4 vp;
        vec3 camera_world_pos;
			};

			in vec4 a_position; 
			in vec2 a_uv0;
      in vec3 a_normal;
	
			out vec3 tc_normal;
			out vec3 tc_pos;
	
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
	      tc_normal = a_normal * mat3(inv_world_transpose);
	      tc_normal = normalize(tc_normal);
        tc_pos = (a_position * world).xyz;
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
        <![CDATA[
			precision mediump float;
		
			layout (std140) uniform view
			{
				mat4 vp;
        vec3 camera_world_pos;
			};
			uniform sampler2D sampler_tex;
		
			in vec3 tc_normal;
			in vec3 tc_pos;
			out vec4 fragColor;
		
			void main()
			{ 
        vec3 uv = normalize(reflect(camera_world_pos - tc_pos.xyz, tc_normal));

        vec2 uv2d = ConvertCubeReflectVector2EquirectangleUV(uv);
        
				gl_FragColor = texture(sampler_tex, uv2d) ;
			}
		]]></GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h" /><![CDATA[
cbuffer cbEnt : register(b0)
{
    matrix world;
    matrix inv_world_transpose;
};
cbuffer cbView : register(b1)
{
    matrix vp;
    float3 camera_world_pos;
};
struct VSInput
{
    float3 pos : POSITION;
    float3 normal: NORMAL;
};

struct VSOutput
{
    float4 pos:SV_POSITION;
    float3 world_pos: TEXCOORD0;
    float3 normal: TEXCOORD1;   
};

SamplerState sampler_tex    : register( s0 );
Texture2D tex_01      : register( t0 );

VSOutput VsMain(VSInput input)
{
    VSOutput o;
    o.pos = mul(float4(input.pos, 1), mul(world, vp));
    o.normal = mul(input.normal, (float3x3)inv_world_transpose);
    o.world_pos = mul(float4(input.pos, 1), world).xyz;
    return o;
}

float4 PsMain(VSOutput input) :SV_TARGET
{
	  float3 uv = normalize(reflect(input.world_pos.xyz - camera_world_pos, input.normal));
    float2 uv2d = ConvertCubeReflectVector2EquirectangleUV(uv);
    return tex_01.Sample(sampler_tex, uv2d);
}
		]]></Shader>
  <Technique name="equirect_cube_reflect">
    <Pass name="pass" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
</Effect>
