<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/texture_scroller">
  <!--必须有name,type；semantic，default, 可选-->
  <Parameter name="emissive" type="Colorf" default= "1,1,1,1"/>
  <Parameter name="tex01" type="Texture2D" semantic="Texture0"/>
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj"/>
  <Parameter name="world" type="Matrix4f" semantic="World"/>
  <Parameter name="vec_scroller" type="Vector4f" default= "1,1,0,0" />
  <Parameter name="is_clip" type="float32" default="0" semantic="EffectGlobal"/>
  <Parameter name="clip_plane" type="Vector4f" default="0,1,0,0" semantic="EffectGlobal"/>
  <!--必须有Texture；其余可选-->
  <Sampler name="sampler01">
    <Texture value="tex01"/>
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR"/>
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP"/>
    <AddressV value="WRAP"/>
    <AddressW value="WRAP"/>
  </Sampler>
  <!--必须有type:hlsl;cg;glsl;目前实现了hlsl-->
	<Shader type="glsl">
		<Include file="shader_buildin/gl_inc.h"/>
		<GLShader name="VsMain" type="Vertex">
			<![CDATA[
			uniform mat4 vp;
			uniform mat4 world;
			uniform vec4 vec_scroller;  
			uniform vec4 clip_plane;
			uniform mediump float is_clip;		
				
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 

			varying vec4 uv;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				
				vec2 tex_s = TRANSFORM_TEXCOORD(a_uv0, vec_scroller);	
				uv.xy = tex_s.xy;
				
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					uv.w = dot(world_pos, clip_plane);
				}
			}
			]]>
		</GLShader>
		<GLShader name="VsMain1" type="Vertex">
			<![CDATA[
			uniform mat4 vp;
			uniform mat4 world;
			uniform vec4 vec_scroller;  
			uniform vec4 clip_plane;
			uniform mediump float is_clip;		
				
			attribute vec4 a_position; 
			attribute vec4 a_uv1; 

			varying vec4 uv;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				
				vec2 tex_s = TRANSFORM_TEXCOORD(a_uv1, vec_scroller);	
				uv.xy = tex_s.xy;
				
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					uv.w = dot(world_pos, clip_plane);
				}
			}
			]]>
		</GLShader>
		<GLShader name="VsMain2" type="Vertex">
			<![CDATA[
			uniform mat4 vp;
			uniform mat4 world;
			uniform vec4 vec_scroller;  
			uniform vec4 clip_plane;
			uniform mediump float is_clip;		
				
			attribute vec4 a_position; 
			attribute vec4 a_uv2; 

			varying vec4 uv;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				
				vec2 tex_s = TRANSFORM_TEXCOORD(a_uv2, vec_scroller);	
				uv.xy = tex_s.xy;
				
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					uv.w = dot(world_pos, clip_plane);
				}
			}
			]]>
		</GLShader>
		<GLShader name="VsMain3" type="Vertex">
			<![CDATA[
			uniform mat4 vp;
			uniform mat4 world;
			uniform vec4 vec_scroller;  
			uniform vec4 clip_plane;
			uniform mediump float is_clip;		
				
			attribute vec4 a_position; 
			attribute vec4 a_uv3; 

			varying vec4 uv;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				
				vec2 tex_s = TRANSFORM_TEXCOORD(a_uv3, vec_scroller);	
				uv.xy = tex_s.xy;
				
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					uv.w = dot(world_pos, clip_plane);
				}
			}
			]]>
		</GLShader>
		<GLShader name="PsMain" type="Pixel">
			<![CDATA[
			precision mediump float;
						
			uniform sampler2D sampler01;
			uniform vec4 emissive;
			
			varying vec4 uv;
			uniform mediump float is_clip;	
	
			void main()
			{ 
				if(is_clip > 0.0 && uv.w < 0.0)  { discard; }
				vec4 clr = texture2D(sampler01, uv.xy);
				gl_FragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a); //ModulateAlpha C = Cs*Cd, A = As*Ad
			}
		]]>
		</GLShader>
	</Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h"/>
    <GLShader name="VsMain" type="Vertex">
      <![CDATA[
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};
			layout (std140) uniform material
			{
				vec4 vec_scroller;
				vec4 emissive;
			};
			layout (std140) uniform global
			{
				vec4 clip_plane;
				mediump float is_clip;
			};
				
			in vec4 a_position; 
			in vec2 a_uv0; 

			out vec4 uv;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				
				vec2 tex_s = TRANSFORM_TEXCOORD(a_uv0, vec_scroller);	
				uv.xy = tex_s.xy;
				
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					uv.w = dot(world_pos, clip_plane);
				}
			}
			]]>
    </GLShader>
    <GLShader name="VsMain1" type="Vertex">
      <![CDATA[
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};
			layout (std140) uniform material
			{
				vec4 vec_scroller;
				vec4 emissive;
			};
			layout (std140) uniform global
			{
				vec4 clip_plane;
				mediump float is_clip;
			};
				
			in vec4 a_position; 
			in vec4 a_uv1; 

			out vec4 uv;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				
				vec2 tex_s = TRANSFORM_TEXCOORD(a_uv1, vec_scroller);	
				uv.xy = tex_s.xy;
				
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					uv.w = dot(world_pos, clip_plane);
				}
			}
			]]>
    </GLShader>
    <GLShader name="VsMain2" type="Vertex">
      <![CDATA[
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};
			layout (std140) uniform material
			{
				vec4 vec_scroller;
				vec4 emissive;
			};
			layout (std140) uniform global
			{
				vec4 clip_plane;
				mediump float is_clip;
			};
				
			in vec4 a_position; 
			in vec4 a_uv2; 

			out vec4 uv;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				
				vec2 tex_s = TRANSFORM_TEXCOORD(a_uv2, vec_scroller);	
				uv.xy = tex_s.xy;
				
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					uv.w = dot(world_pos, clip_plane);
				}
			}
			]]>
    </GLShader>
    <GLShader name="VsMain3" type="Vertex">
      <![CDATA[
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};
			layout (std140) uniform material
			{
				vec4 vec_scroller;
				vec4 emissive;
			};
			layout (std140) uniform global
			{
				vec4 clip_plane;
				mediump float is_clip;
			};
				
			in vec4 a_position; 
			in vec4 a_uv3; 

			out vec4 uv;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				
				vec2 tex_s = TRANSFORM_TEXCOORD(a_uv3, vec_scroller);	
				uv.xy = tex_s.xy;
				
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					uv.w = dot(world_pos, clip_plane);
				}
			}
			]]>
    </GLShader>
    <GLShader name="PsMain" type="Pixel">
      <![CDATA[
			precision mediump float;
						
			uniform sampler2D sampler01;
			
			layout (std140) uniform material
			{
				vec4 vec_scroller;
				vec4 emissive;
			};
			
			in vec4 uv;
			out vec4 fragColor;

			layout (std140) uniform global
			{
				vec4 clip_plane;
				mediump float is_clip;
			};
	
			void main()
			{ 
				if(is_clip > 0.0 && uv.w < 0.0)  { discard; }
				vec4 clr = texture(sampler01, uv.xy);
				fragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a); //ModulateAlpha C = Cs*Cd, A = As*Ad
			}
		]]>
    </GLShader>
  </Shader>
  <Shader type="hlsl5">
    <!--一个Shader可以有零个或多个Include，必须有file：指定文件名-->
    <Include file="shader_buildin/dx_inc.h"/>
    <![CDATA[
    cbuffer cbEntity : register( b0 )
    {
		matrix world;
    };
    cbuffer cbView : register( b3 )
    {
    matrix vp;
    };

    cbuffer cbMaterial : register( b1 )
    {
    float4 emissive;    
    float4 vec_scroller;  
    };
    
    cbuffer cbGlobal : register( b2 )
    {
    float4 clip_plane;
    float is_clip;
    };

    SamplerState sampler01 : register( s0 );
    Texture2D tex01 : register( t0 );
    
    struct VsInput
    {
      float3 pos : POSITION;
      float2 uv : TEXCOORD0;
    };
	
	struct VsInput1
    {
      float3 pos : POSITION;
      float2 uv : TEXCOORD1;
    };
    
	struct VsInput2
    {
      float3 pos : POSITION;
      float2 uv : TEXCOORD2;
    };
	
	struct VsInput3
    {
      float3 pos : POSITION;
      float2 uv : TEXCOORD3;
    };
	
	struct VsInput4
    {
      float3 pos : POSITION;
      float2 uv : TEXCOORD4;
    };
	
    struct VsOutput
    {
      float4 pos : SV_POSITION;
      float4 uv : TEXCOORD0;
    };
    
    VsOutput VsMain(VsInput input)
    {
      VsOutput o = (VsOutput)0;
      o.pos = mul(float4(input.pos, 1), MUL(world, vp));

      float2 tex_s = TRANSFORM_TEXCOORD(input.uv, vec_scroller);	
      o.uv.xy = tex_s.xy;
      
      if(is_clip > 0)
      {
        float4 world_pos = mul(float4(input.pos, 1), world);
        o.uv.w = dot(world_pos, clip_plane);
      }
      
      return o;
    }
	
	VsOutput VsMain1(VsInput1 input)
    {
      VsOutput o = (VsOutput)0;
      o.pos = mul(float4(input.pos, 1), MUL(world, vp));
      float2 tex_s = TRANSFORM_TEXCOORD(input.uv, vec_scroller);	
      o.uv.xy = tex_s.xy;
      
      if(is_clip > 0)
      {
        float4 world_pos = mul(float4(input.pos, 1), world);
        o.uv.w = dot(world_pos, clip_plane);
      }
      
      return o;
    }
	
	VsOutput VsMain2(VsInput2 input)
    {
      VsOutput o = (VsOutput)0;
      o.pos = mul(float4(input.pos, 1), MUL(world, vp));
      float2 tex_s = TRANSFORM_TEXCOORD(input.uv, vec_scroller);	
      o.uv.xy = tex_s.xy;
      
      if(is_clip > 0)
      {
        float4 world_pos = mul(float4(input.pos, 1), world);
        o.uv.w = dot(world_pos, clip_plane);
      }
      
      return o;
    }
	
	VsOutput VsMain3(VsInput3 input)
    {
      VsOutput o = (VsOutput)0;
      o.pos = mul(float4(input.pos, 1), MUL(world, vp));
      float2 tex_s = TRANSFORM_TEXCOORD(input.uv, vec_scroller);	
      o.uv.xy = tex_s.xy;
      
      if(is_clip > 0)
      {
        float4 world_pos = mul(float4(input.pos, 1), world);
        o.uv.w = dot(world_pos, clip_plane);
      }
      
      return o;
    }
	
	VsOutput VsMain4(VsInput4 input)
    {
      VsOutput o = (VsOutput)0;
      o.pos = mul(float4(input.pos, 1), MUL(world, vp));
      float2 tex_s = TRANSFORM_TEXCOORD(input.uv, vec_scroller);	
      o.uv.xy = tex_s.xy;
      
      if(is_clip > 0)
      {
        float4 world_pos = mul(float4(input.pos, 1), world);
        o.uv.w = dot(world_pos, clip_plane);
      }
      
      return o;
    }
    
    float4 PsMain(VsOutput input) : SV_Target
    {	 
      if(is_clip > 0)  { clip(input.uv.w); }
      float4 clr = tex01.Sample(sampler01, input.uv.xy);
	    return float4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a); //ModulateAlpha C = Cs*Cd, A = As*Ad
    }
    ]]>
  </Shader>
  <Technique name="TextureDiffuse">
    <Pass name="pass1" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0"/>
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0"/>
    </Pass>
  </Technique>
  <Technique name="TextureDiffuse1">
    <Pass name="pass1" restorestate="false">
      <State name="VertexShader" value="VsMain1" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0"/>
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0"/>
    </Pass>
  </Technique>
  <Technique name="TextureDiffuse2">
    <Pass name="pass1" restorestate="false">
      <State name="VertexShader" value="VsMain2" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0"/>
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0"/>
    </Pass>
  </Technique>
  <Technique name="TextureDiffuse3">
    <Pass name="pass1" restorestate="false">
      <State name="VertexShader" value="VsMain3" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0"/>
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0"/>
    </Pass>
  </Technique>
</Effect>

