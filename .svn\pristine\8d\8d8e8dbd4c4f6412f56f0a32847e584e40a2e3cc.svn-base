﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace PicWorkStationWindowsFormsForALi
{
    class Helper
    {
        public static string ToString(object obj)
        {
            if (obj == null) return string.Empty;

            StringBuilder ret = new StringBuilder();

            var type = obj.GetType();
            var props = type.GetFields(BindingFlags.Public | BindingFlags.Instance);
            foreach (var item in props)
            {
                ret.Append(string.Format("{0}: {1};", item.Name, item.GetValue(obj) != null ? item.GetValue(obj).ToString() : "null"));
            }

            return ret.ToString();
        }

        public static void CopyDirectory(string srcDir, string destDir)
        {
            var fs = Directory.GetFiles(srcDir, "*", SearchOption.TopDirectoryOnly);
            foreach (var f in fs) 
            {
                File.Copy(f, Path.Combine(destDir, Path.GetFileName(f)), true);
            }            
        }
    }
}
