﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!--康利测试-->
    <!--<add key="DataServer" value="http://edp.5000m.com:28753/" />
    <add key="FileServer" value="http://edp.5000m.com:9050/" />-->

    <!--<add key="DataServer" value="http://localhost:28753" />
    <add key="FileServer" value="http://*************:28665" />-->
    <!--西奥-->
    <!--<add key="DataServer" value="http://*************:28665" />
    <add key="FileServer" value="http://*************:28665" />
    <add key="EcsServer" value="http://*************:8070" />-->
    <!--日本日立-->
    <!--<add key="DataServer" value="http://*************:28666" />
    <add key="FileServer" value="http://*************:28666" />
    <add key="EcsServer" value="http://*************:8070" />-->
    <!--日本日立test-->
    <add key="DataServer" value="http://*************:28656" />
    <add key="FileServer" value="http://*************:28656" />
    <add key="EcsServer" value="http://*************:8070" />
    <add key="ProxyUrl" value="" />
    <add key="ProxyName" value="" />
    <add key="ProxyPsw" value="" />
    <add key="DisableCounter" value="0" />
    <add key="FormTimerTick" value="30" />
    <add key="TitlePrefix" value="" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
    <add key="CutBackgroundX" value="255"/>
    <add key="IsTestVersion" value="1"/>
	  <add key="NotUsedZipCompress" value="true"/>
  </appSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
  </startup>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
</configuration>