<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/alpha_reflect">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj" />
  <Parameter name="world" type="Matrix4f" semantic="World" />
  <Parameter name="tex_01" type="Texture2D" semantic="Texture0" />
  <Parameter name="tex_lm" type="Texture2D" semantic="Texture1" />
  <Parameter name="tex_channel" type="Texture2D" semantic="Texture2" />
  <Parameter name="vec_tex" type="Vector4f" default="1,1,0,0" />
  <Parameter name="vec_channel" type="Vector4f" default="1,1,0,0" />
  <Parameter name="plate_emissive" type="Colorf" default="1,1,1,1" />
  <Parameter name="is_clip" type="float32" default="0" semantic="EffectGlobal" />
  <Parameter name="clip_plane" type="Vector4f" default="0,1,0,0" semantic="EffectGlobal" />
  <Sampler name="sampler_tex">
    <Texture value="tex_01" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP" />
    <AddressV value="WRAP" />
    <AddressW value="WRAP" />
  </Sampler>
  <Sampler name="sampler_lm">
    <Texture value="tex_lm" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="CLAMP" />
    <AddressV value="CLAMP" />
    <AddressW value="CLAMP" />
  </Sampler>
  <Sampler name="sampler_channel">
    <Texture value="tex_channel" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP" />
    <AddressV value="WRAP" />
    <AddressW value="WRAP" />
  </Sampler>
  <Shader type="glsl">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			uniform mat4 vp;
			uniform mat4 world;
			uniform vec4 vec_tex;   
			uniform vec4 vec_channel;  
			
			uniform vec4 clip_plane; 
			uniform mediump float is_clip;
		
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 
		
			varying vec2 tc_lm;
			varying vec4 tc_tex;
			varying vec2 tc_channel;
		
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
				//LM				
				tc_lm      = a_uv0.xy;
				//Tex	
				tc_tex.xy     = a_uv0.xy;
				vec4 tex_k  = vec4(tc_tex.x, tc_tex.y, 0.0, 1.0);
				vec2 tex_l  = TRANSFORM_TEXCOORD(tex_k, vec_tex);	
				tc_tex.xy  = tex_l;
				//Channel	
				tc_channel = a_uv0.xy;
				vec4 channel_k  = vec4(tc_channel.x, tc_channel.y, 0.0, 1.0);
				vec2 channel_l = TRANSFORM_TEXCOORD(channel_k, vec_channel);	
				tc_channel = channel_l;
					
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					tc_tex.w = dot(world_pos, clip_plane);
				}
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel"><![CDATA[
			precision mediump float;
		
			uniform vec4 plate_emissive; 
			uniform vec4 clip_plane; 
			uniform mediump float is_clip;
		
			uniform sampler2D sampler_lm;
			uniform sampler2D sampler_tex;
			uniform sampler2D sampler_channel;	
		
			varying vec2 tc_lm;
			varying vec4 tc_tex;
			varying vec2 tc_channel;
		
			void main()
			{
				if(is_clip > 0.0 && tc_tex.w < 0.0)  { discard; }
		
				vec4 col_lm        =  texture2D(sampler_lm, tc_lm) ;
				vec4 col_tex       =  texture2D(sampler_tex, tc_tex.xy) ;
				vec4 col_channel   =  texture2D(sampler_channel, tc_channel);
				vec4 clr_res = col_lm *  col_tex * plate_emissive * 2.0 ; 
				clr_res.w = col_channel.w * col_tex.w;
				gl_FragColor = clr_res;
			}
		]]></GLShader>
  </Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};
			layout (std140) uniform material
			{
				vec4 vec_tex;
				vec4 vec_channel;
				vec4 plate_emissive;
			};
			layout (std140) uniform global
			{
				vec4 clip_plane;
				mediump float is_clip;
			};
		
			in vec4 a_position; 
			in vec2 a_uv0; 
		
			out vec2 tc_lm;
			out vec4 tc_tex;
			out vec2 tc_channel;
		
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
				//LM				
				tc_lm      = a_uv0.xy;
				//Tex	
				tc_tex.xy     = a_uv0.xy;
				vec4 tex_k  = vec4(tc_tex.x, tc_tex.y, 0.0, 1.0);
				vec2 tex_l  = TRANSFORM_TEXCOORD(tex_k, vec_tex);	
				tc_tex.xy  = tex_l;
				//Channel	
				tc_channel = a_uv0.xy;
				vec4 channel_k  = vec4(tc_channel.x, tc_channel.y, 0.0, 1.0);
				vec2 channel_l = TRANSFORM_TEXCOORD(channel_k, vec_channel);	
				tc_channel = channel_l;
					
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					tc_tex.w = dot(world_pos, clip_plane);
				}
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel"><![CDATA[
			precision mediump float;
			layout (std140) uniform material
			{
				vec4 vec_tex;
				vec4 vec_channel;
				vec4 plate_emissive;
			};
			layout (std140) uniform global
			{
				vec4 clip_plane;
				mediump float is_clip;
			};
		
			uniform sampler2D sampler_lm;
			uniform sampler2D sampler_tex;
			uniform sampler2D sampler_channel;	
		
			in vec2 tc_lm;
			in vec4 tc_tex;
			in vec2 tc_channel;
			out vec4 fragColor;
		
			void main()
			{
				if(is_clip > 0.0 && tc_tex.w < 0.0)  { discard; }
		
				vec4 col_lm        =  texture(sampler_lm, tc_lm) ;
				vec4 col_tex       =  texture(sampler_tex, tc_tex.xy) ;
				vec4 col_channel   =  texture(sampler_channel, tc_channel);
				vec4 clr_res = col_lm *  col_tex * plate_emissive * 2.0 ; 
				clr_res.w = col_channel.w * col_tex.w;
				fragColor = clr_res;
			}
		]]></GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h" /><![CDATA[
    cbuffer cbEntity : register( b0 )
    {
		float4x4 world; 
    };
    cbuffer cbView : register( b3 )
    {
		float4x4 vp;
    };
    
    cbuffer cbMaterial : register( b1 )
    {
		float4 vec_tex;   
		float4 vec_channel;    
		float4 plate_emissive;
    };
    
    cbuffer cbGlobal : register( b2 )
    {
    float4 clip_plane;
    float is_clip;
    };
    
    SamplerState sampler_tex : register( s0 );
    Texture2D tex_01 : register( t0 );

		SamplerState sampler_lm : register( s1 );
    Texture2D tex_lm : register( t1 );
 
		SamplerState sampler_channel : register( s2 );
    Texture2D tex_channel : register( t2 );


		struct VsInput
		{
		  float3 pos:POSITION;
		  float4 tc :TEXCOORD0; 
		};
    
		struct VsOutput
		{
		  float4   pos             :   SV_POSITION;
		  float2   tc_lm           :   TEXCOORD0;
		  float4   tc_tex          :   TEXCOORD1;
		  float2   tc_channel      :   TEXCOORD2;
		};
    
		VsOutput VsMain(VsInput input)
		{
			VsOutput o = (VsOutput)0;
			float4 r0=mul(float4(input.pos,1),MUL(world, vp));
			o.pos = r0;
			//LM				
			o.tc_lm      = input.tc.xy;
			//Tex	
			o.tc_tex     = input.tc;
			float4 tex_k  = float4(o.tc_tex.x, o.tc_tex.y, 0, 1);
			float2 tex_l  = TRANSFORM_TEXCOORD(tex_k, vec_tex);	
			o.tc_tex.xy  = tex_l;
			//Channel	
			o.tc_channel     = input.tc;
			float4 channel_k  = float4(o.tc_channel.x, o.tc_channel.y, 0, 1);
			float2 channel_l  = TRANSFORM_TEXCOORD(channel_k, vec_channel);	
			o.tc_channel.xy  = channel_l;
		
			if(is_clip > 0)
			{
				float4 world_pos = mul(float4(input.pos, 1), world);
				o.tc_tex.w = dot(world_pos, clip_plane);
			}
		
			return o;
		}
    
		float4 PsMain(VsOutput input) : SV_TARGET
		{
			if(is_clip > 0)  { clip(input.tc_tex.w); }
		
			float4 col_lm        =  tex_lm.Sample(sampler_lm,input.tc_lm) ;
			float4 col_tex       =  tex_01.Sample(sampler_tex,input.tc_tex) ;
			float4 col_channel   =  tex_channel.Sample(sampler_channel,input.tc_channel);

			float4 clr_res = col_lm *  col_tex * plate_emissive * 2 ; 

			clr_res.w = col_channel.w * col_tex.w;

			return clr_res;
		}
		]]></Shader>
  <Technique name="alpha_shader_tech">
    <Pass name="pass" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
      <State name="DepthBias" value="-1" />
    </Pass>
  </Technique>
</Effect>
