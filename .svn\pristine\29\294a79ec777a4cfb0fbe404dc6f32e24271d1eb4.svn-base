﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PicWorkStationWindowsFormsForALi
{
    class WordReportTaskProcess : DefaultTaskProcess
    {
        public WordReportTaskProcess(ITaskServer taskServer)
            : base(taskServer)
        {
        }
        public override bool IsCanProcess(TaskInfo info)
        {
            return info.TaskType == Const.TaskType_WordReportTask;
        }

        public override void DoProcessPreRunTask(TaskInfo taskInfo, object param)
        {
            base.DoProcessPreRunTask(taskInfo, param);
        }

        public override bool DoProcessAfterEndTask(TaskInfo taskInfo, object param)
        {
            return base.DoProcessAfterEndTask(taskInfo, param);
        }
    }
}
