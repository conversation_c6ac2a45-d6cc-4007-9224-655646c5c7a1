﻿using Ionic.Zip;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Security.AccessControl;
using System.Text;
using System.Threading.Tasks;

namespace PicWorkStationWindowsFormsForALi
{
    class DefaultTaskProcess : TaskProcess
    {
        protected List<PartDetailInfo> _partInfos;

        public DefaultTaskProcess(ITaskServer taskServer)
        {
            TaskServer = taskServer;
        }

        public override bool IsZipCompress(TaskInfo info)
        {
            var ret = base.IsZipCompress(info);

            if (ConfigurationManager.AppSettings.AllKeys.Contains("NotUsedZipCompress"))
            {
                var testVersion = ConfigurationManager.AppSettings["NotUsedZipCompress"];

                var n = false;
                if(bool.TryParse(testVersion, out n)) ret = !n;
            }

            return ret;
        }
        public override bool IsCanProcess(TaskInfo info)
        {
            return info.TaskType == Const.TaskType_DefaultTask;
        }

        public override void DoProcessPreRunTask(TaskInfo info, object param)
        {
            DoProcessByMultilayer(0, info, param);
        }
        public override bool DoProcessAfterEndTask(TaskInfo taskInfo, object param)
        {
            try
            {
                TaskServer.WriteLog(Const.KEY_START_COPY_FILE_TO_SHARED_FOLDERS, Const.MSG_TYPE_Normal);

                var path = Path.Combine(taskInfo.SharedFolder, taskInfo.NewFileDir);
                CreateDirectory(path);

                if (IsZipCompress(taskInfo))
                {
                    if (Path.DirectorySeparatorChar != path[path.Length - 1]) path += Path.DirectorySeparatorChar;

                    string zipFileToCreate = path + "output.zip";
                    string directoryToZip = taskInfo.WorkPath;
                    CreateZipFile(zipFileToCreate, directoryToZip);
                }
                else
                {
                    TaskServer.WriteLog("It's not zip compress.", Const.MSG_TYPE_Normal);
                    Helper.CopyDirectory(taskInfo.WorkPath, path);
                }

                Directory.Delete(taskInfo.WorkPath, true);

                string workPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, taskInfo.NewFileDir);
                if (Directory.Exists(workPath)) Directory.Delete(workPath, true);

                TaskServer.WriteLog(Const.KEY_END_COPY_FILE_TO_SHARED_FOLDERS, Const.MSG_TYPE_Normal);
                return true;
            }
            catch (Exception ex)
            {
                TaskServer.WriteLog(string.Format(Const.KEY_COPY_FILE_EXCEPTION, ex), Const.MSG_TYPE_ERROR);

                return false;
            }
        }
        public override void DoProcessByMultilayer(int multilayer, TaskInfo info, object param)
        {
            _partInfos = Const.partDetails[multilayer][info.TaskType];

            var snapInfoArray = TaskServer.VRInterface.GetSceneArrayVisitor(Const.AUTO_SNAP_ARRAY_NAME);
            if (snapInfoArray != null)
            {
                snapInfoArray.Clear();
                var datas = _partInfos.Select(e => e.FixViewID).ToArray();
                for (var i = 0; i < datas.Length; ++i)
                {
                    snapInfoArray.AddRow();
                    snapInfoArray.SetElementValue(i, 0, datas[i]);
                }
            }
            if (info.OutFiles == null) info.OutFiles = new List<object>();
            info.OutFiles.Clear();
            info.OutFiles.AddRange(_partInfos);
        }

        public String CreateDirectory(String path)
        {
            try
            {
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                    var dirSecurity = new DirectorySecurity();
                    var rule = new FileSystemAccessRule(
                        "Everyone",
                        FileSystemRights.FullControl,
                        InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit,
                        PropagationFlags.None,
                        AccessControlType.Allow);
                    dirSecurity.AddAccessRule(rule);
                    Directory.SetAccessControl(path, dirSecurity);
                }
            }
            catch (Exception e)
            {
                TaskServer.WriteLog(string.Format(Const.KEY_CREATE_DIR_ERROR, path), Const.MSG_TYPE_ERROR);
                throw e;
            }

            return path;
        }

        public void CreateZipFile(string zipFileToCreate, string directoryToZip)
        {
            try
            {
                using (ZipFile zip = new ZipFile())
                {
                    zip.AddDirectory(directoryToZip);
                    zip.Save(zipFileToCreate);
                }
            }
            catch (System.Exception ex1)
            {
                TaskServer.WriteLog(string.Format(Const.KEY_CREATE_ZIP_ERROR, zipFileToCreate, directoryToZip), Const.MSG_TYPE_ERROR);
                TaskServer.WriteLog(ex1.Message, Const.MSG_TYPE_ERROR);
            }
        }
    }

    public class Class1
    {
    }
}
