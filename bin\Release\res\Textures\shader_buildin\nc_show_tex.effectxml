<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/nc_show_tex">
	<!--必须有name,type；semantic，default可选-->
	<Parameter name="tex_01" type="Texture2D" semantic="Texture0"/>
  <Parameter name="emissive" type="Colorf" default= "1,1,1,1" />
	<Sampler name="sampler_tex">
		<Texture value="tex_01"/>
		<!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
		<Filter value="MIN_MAG_MIP_LINEAR"/>
		<!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
		<AddressU value="WRAP"/>
		<AddressV value="WRAP"/>
		<AddressW value="WRAP"/>
	</Sampler>

	<Shader type="glsl">
		<Include file="shader_buildin/gl_inc.h"/>
		<GLShader name="VsMain" type="Vertex">
			<![CDATA[
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 

			varying vec2 tc_tex;
		
			void main() 
			{
				gl_Position = a_position;

				//Tex	
				tc_tex = a_uv0.xy;				
			}
			]]>
		</GLShader>
		<GLShader name="PsMain" type="Pixel">
			<![CDATA[
			precision mediump float;
		
			uniform sampler2D sampler_tex;
      uniform vec4 emissive;
			varying vec2 tc_tex;

			void main()
			{ 				
				gl_FragColor       =  texture2D(sampler_tex, tc_tex) * emissive;
			}
		]]>
		</GLShader>
	</Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h"/>
    <![CDATA[
	 
		SamplerState sampler_tex : register( s0 );
    Texture2D tex_01 : register( t0 );

    cbuffer cbMaterial : register( b0 )
    {
		float4 emissive;
    };
    
		struct VsInput
		{
		  float3 pos:POSITION;
		  float2 tc :TEXCOORD0; 
		};
    
		struct VsOutput
		{
		  float4   pos             :   SV_POSITION;
		  float2   tc_tex           :   TEXCOORD0;
		};
    
		VsOutput VsMain(VsInput input)
		{
			VsOutput o = (VsOutput)0;
			o.pos = float4(input.pos, 1);

			//Tex	
			o.tc_tex     = input.tc;

			return o;
		}
    
		float4 PsMain(VsOutput input) : SV_TARGET
		{
			float4 col_tex =  tex_01.Sample(sampler_tex,input.tc_tex) ;		
			return col_tex * emissive;
		}
		]]>
  </Shader>
  <Technique name="nc_show_tex">
		<Pass name="pass" restorestate="false">
			<State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0"  gles_profile="vs_1_0"/>
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0"  dx11_profile="ps_4_0" gles_profile="ps_1_0"/>
		</Pass>
	</Technique>
</Effect>
