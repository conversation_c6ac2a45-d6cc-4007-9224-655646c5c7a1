﻿<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/shader_mirror">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="world" type="Matrix4f" semantic="World" />
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj" />
  <Parameter name="v" type="Matrix4f" semantic="View" />
  <Parameter name="tex_01" type="Texture2D" semantic="Texture0" />
  <Parameter name="tex_lm" type="Texture2D" semantic="Texture1" />
  <Parameter name="tex_ref" type="Texture2D" semantic="Texture2" />
  <Parameter name="vec_tex" type="Vector4f" default="1,1,0,0" />
  <Parameter name="vec_ori" type="Vector4f" default="1,1,0,0" />
  <Parameter name="vec_ref" type="Vector4f" default="1,1,0,0" />
  <!--
  x, is main obj, 1:main; -1:aux; 
  y, ref mode 1:shader reflect; -1:aux reflect; 
  z, is_hairline 0:hair line; 1:paint;
  -->
  <Parameter name="setting" type="Vector4f" default="1,1,0,0" />
  <Parameter name="tp" type="Matrix4f" />
  <Parameter name="brightness_ambient" type="Colorf" default="1,1,1,1" />
  <Parameter name="reflect_strength_diffuse" type="Colorf" default="1,1,1,1" />
  <Parameter name="plate_emissive" type="Colorf" default="1,1,1,1" />
  <Parameter name="is_clip" type="float32" default="0" semantic="EffectGlobal" />
  <Parameter name="clip_plane" type="Vector4f" default="0,1,0,0" semantic="EffectGlobal" />
  <Sampler name="sampler_tex">
    <Texture value="tex_01" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP" />
    <AddressV value="WRAP" />
    <AddressW value="WRAP" />
  </Sampler>
  <Sampler name="sampler_lm">
    <Texture value="tex_lm" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="CLAMP" />
    <AddressV value="CLAMP" />
    <AddressW value="CLAMP" />
  </Sampler>
  <Sampler name="sampler_ref">
    <Texture value="tex_ref" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="CLAMP" />
    <AddressV value="CLAMP" />
    <AddressW value="CLAMP" />
  </Sampler>
  <Shader type="glsl">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			uniform mat4 world;
			uniform mat4 vp;
			uniform mat4 v;
			uniform vec4 vec_tex;   
			uniform vec4 vec_ori; 
			uniform vec4 vec_ref;  
      uniform mediump vec4 setting;
			uniform mat4 tp;
      
			uniform vec4 clip_plane;
			uniform float is_clip;
		
    	attribute vec4 a_position;
			attribute vec2 a_uv0;

			varying vec4 tc_ref; 
			varying vec2 tc_lm;
			varying vec4 tc_tex;
			
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;	

        tc_lm      = a_uv0;		
				tc_tex.xy  = ROTATE_TEXCOORD(a_uv0, vec_ori);	
				tc_tex.xy  = TRANSFORM_TEXCOORD(tc_tex, vec_tex);	
        
        tc_ref = ComputeReflectUVInVS(setting, world, v, 
        tp, a_position, a_uv0, vec_ref);
        ComputeClipInVS(tc_tex, a_position, setting, 
        is_clip, clip_plane, world);
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
        <![CDATA[
			precision mediump float;
			
			uniform sampler2D sampler_lm;
			uniform sampler2D sampler_tex;
			uniform sampler2D sampler_ref;
		
			uniform vec4 brightness_ambient;
			uniform vec4 reflect_strength_diffuse;
			uniform vec4 plate_emissive;
      uniform mediump vec4 setting;
		
			varying vec4 tc_ref;
			varying vec2 tc_lm;
			varying vec4 tc_tex;
	
			void main()
			{ 
        PS_COMPUTE_CLIP;
        vec4 col_ref = ComputeReflectColorInPS(sampler_ref, tc_ref, setting);

				vec4 col_lm        = texture2D( sampler_lm, tc_lm) ;
				vec4 col_tex       = texture2D( sampler_tex, tc_tex.xy) ;
				vec4  clr_res       = col_lm * ( col_tex * plate_emissive + col_ref * reflect_strength_diffuse ) * brightness_ambient * 2.0; 
				clr_res.w             = 1.0;
				gl_FragColor = clr_res;
			}
		]]></GLShader>
  </Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 v;
				mat4 vp;
			};
			layout (std140) uniform material
			{
				mat4 tp;
				vec4 vec_tex;
				vec4 vec_ori;
				vec4 brightness_ambient;
				vec4 reflect_strength_diffuse;
				vec4 plate_emissive;
				mediump vec4 setting;
        vec4 vec_ref;
			};
			layout (std140) uniform global
			{
			   vec4 clip_plane;
			   float is_clip;
			};
			
			in vec4 a_position; 
			in vec2 a_uv0; 
		
			out vec4 tc_ref; 
			out vec2 tc_lm;
			out vec4 tc_tex;
			
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;	

        tc_lm      = a_uv0;		
				tc_tex.xy  = ROTATE_TEXCOORD(a_uv0, vec_ori);	
				tc_tex.xy  = TRANSFORM_TEXCOORD(tc_tex, vec_tex);	
        
        tc_ref = ComputeReflectUVInVS(setting, world, v, 
        tp, a_position, a_uv0, vec_ref);
        
        ComputeClipInVS(tc_tex, a_position, setting, 
        is_clip, clip_plane, world);

			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
      <![CDATA[
			precision mediump float;
			
			uniform sampler2D sampler_lm;
			uniform sampler2D sampler_tex;
			uniform sampler2D sampler_ref;
		
			layout (std140) uniform material
			{
				mat4 tp;
				vec4 vec_tex;
				vec4 vec_ori;
				vec4 brightness_ambient;
				vec4 reflect_strength_diffuse;
				vec4 plate_emissive;
				mediump vec4 setting;
        vec4 vec_ref;
			};
			
			in vec4 tc_ref; 
			in vec2 tc_lm;
			in vec4 tc_tex;
			out vec4 fragColor;
	
			void main()
			{ 
        PS_COMPUTE_CLIP;
        vec4 col_ref = ComputeReflectColorInPS(sampler_ref, tc_ref, setting);

				vec4 col_lm        = texture( sampler_lm, tc_lm) ;
				vec4 col_tex       = texture( sampler_tex, tc_tex.xy) ;
				vec4  clr_res       = col_lm * ( col_tex * plate_emissive + col_ref * reflect_strength_diffuse ) * brightness_ambient * 2.0; 
				clr_res.w             = 1.0;
				fragColor = clr_res;
			}
		]]></GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h" /><![CDATA[
    cbuffer cbEntity : register( b0 )
    {
    matrix world;
    };
    
    cbuffer cbView : register( b2 )
    {
    matrix v;   
    matrix vp;
    };

    cbuffer cbMaterial : register( b1 )
    {
	  matrix tp;
    float4 vec_tex;   
    float4 vec_ori;   
    float4 vec_ref;
	  float4 brightness_ambient;
	  float4 reflect_strength_diffuse;
    float4 plate_emissive;
		float4 setting;
	  };
    
    cbuffer cbGlobal : register( b3 )
    {
    float4 clip_plane;
    float is_clip;
    };
    
    SamplerState sampler_tex : register( s0 );
    Texture2D tex_01 : register( t0 );
    
    SamplerState sampler_lm : register( s1 );
    Texture2D tex_lm : register( t1 );
    
    SamplerState sampler_ref : register( s2 );
    Texture2D tex_ref : register( t2 );
	
	
    struct VsInput
    {
     float3 pos:POSITION;
	   float2 tc :TEXCOORD; 
    };
    
    struct VsOutput
    {
		float4   pos             :   SV_POSITION;
		float4   tc_ref          :   TEXCOORD0 ;
		float2   tc_lm           :   TEXCOORD1;
		float4   tc_tex          :   TEXCOORD2;	
    };
    
    VsOutput VsMain(VsInput input)
    {
		  VsOutput o = (VsOutput)0;
	
	    float4   r0=mul(float4(input.pos,1),MUL(world, vp));
		  o.pos=r0;	
		  o.tc_lm      = input.tc;		
		  o.tc_tex.xy  = ROTATE_TEXCOORD(input.tc.xy,vec_ori);	
		  o.tc_tex.xy  = TRANSFORM_TEXCOORD(o.tc_tex.xy, vec_tex);	

      VS_COMPUTE_REFLECT_UV;
      VS_COMPUTE_CLIP;

	    return o;
    }

    float4 PsMain(VsOutput input) : SV_Target
    {
      PS_COMPUTE_CLIP;
		  PS_COMPUTE_REFLECT;

		  float4 col_lm        = tex_lm.Sample( sampler_lm,input.tc_lm) ;
		  float4 col_tex       = tex_01.Sample( sampler_tex,input.tc_tex.xy) ;
		  float4  clr_res       = col_lm * ( col_tex * plate_emissive + col_ref * reflect_strength_diffuse ) * brightness_ambient * 2; 
		  clr_res.w             = 1.0;

		  return clr_res;
    }
    ]]></Shader>
  <Technique name="base_shader_tech">
    <Pass name="pass" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
</Effect>