﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Drawing.Imaging;

namespace PicWorkStationWindowsFormsForALi
{
    public class PostEffectOfImageCutBackground : IImageItemProcess
    {
        public PostEffectOfImageCutBackground()
        {
            //SkipBottom = 0;
            //SkipTop = 0;
            //SkipLeft = 2;
            //SkipRight = 2;

            SkipHeight = 0;
            SkipWidth = 2;
        }

        #region IImageItemProcess 成员
        public string CreateMark { get; set; }
        public int ColorNumber { get; set; }

        public int SkipHeight { get; set; }
        public int SkipWidth { get; set; }
        //public int SkipTop { get; set; }
        //public int SkipBottom { get; set; }

        public void DoProcess(int reportId, string param)
        {
            if (!File.Exists(param)) return;

            System.Drawing.Bitmap bitmap = (System.Drawing.Bitmap)System.Drawing.Bitmap.FromFile(param);

            //抠图
            int scanWidth = bitmap.Width / 2,
                scanHeight = bitmap.Height / 2;

            int bmpWidth = bitmap.Width - 1, bmpHeight = bitmap.Height - 1;

            int startLeft = 0, startTop = 0,
                endRight = bmpWidth, endBottom = bmpHeight,
                horzCheckLap = (int)(scanHeight * 0.75),  //水平扫描的条带宽度
                vertHalfCheckLap = (int)(scanWidth * 0.9); //垂直扫描的条带宽度，有些深轿厢用50不能满足，所以改为宽度的1/3

            bool bFindStart = false, bFindEnd = false;

            //find top edge, bottom edge
            for (int y = SkipHeight; y < bmpHeight; ++y)
            {
                for (int x = scanWidth - vertHalfCheckLap; x < scanWidth + vertHalfCheckLap; ++x)
                {
                    var color = bitmap.GetPixel(x, y);
                    if (!bFindStart && !IsColorKey(color))
                    {
                        bFindStart = true;
                        startTop = y;
                    }

                    color = bitmap.GetPixel(x, bmpHeight - y);
                    if (!bFindEnd && !IsColorKey(color))
                    {
                        bFindEnd = true;
                        endBottom = bitmap.Height - y;
                    }

                    if (bFindEnd && bFindStart) break;
                }

                if (bFindEnd && bFindStart) break;
            }

            bFindStart = bFindEnd = false;
            //left edge, right edge
            for (int x = SkipWidth; x < scanWidth; ++x)
            {
                for (int y = startTop; y < endBottom; ++y)
                {
                    var color = bitmap.GetPixel(x, y);
                    if (!bFindStart && !IsColorKey(color))
                    {
                        bFindStart = true;
                        startLeft = x;
                    }

                    color = bitmap.GetPixel(bmpWidth - x, y);
                    if (!bFindEnd && !IsColorKey(color))
                    {
                        bFindEnd = true;
                        endRight = bitmap.Width - x;
                    }

                    if (bFindEnd && bFindStart) break;
                }

                if (bFindEnd && bFindStart) break;
            }

            //
            System.Drawing.Rectangle rc = new System.Drawing.Rectangle(startLeft, startTop,
                endRight - startLeft, endBottom - startTop);

            System.Drawing.Bitmap bmp = bitmap.Clone(rc, bitmap.PixelFormat);
            if (reportId == 1)
            {
                String logoPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "logo.png");
                if(File.Exists(logoPath))
                {
                    imgWater(bmp, logoPath, 50);
                }
            }

            //先释放源图才能保存新图，因为新图要替换源图
            bitmap.Dispose();

            var path = param/*.Replace(".bmp", ".jpg")*/;
            bmp.Save(path, ImageFormat.Jpeg);
            bmp.Dispose();
            /*try
            {
                File.Delete(param);
            }
            catch (Exception)
            {
                
                throw;
            }*/
        }

        #endregion

        private bool IsColorKey(System.Drawing.Color color)
        {
            return (IsInRange(color.R) && IsInRange(color.G) && IsInRange(color.B));
        }

        private bool IsInRange(byte clr)
        {
            return Math.Abs(clr - ColorNumber) < 1;
        }

        public bool imgWater(System.Drawing.Bitmap bmp, string waterImageFile, int horizontalMargin)
        {
            try
            {
                var srcImage = bmp;
                int srcImageWidth = srcImage.Width;
                int srcImageHeight = srcImage.Height;
                var waterImage = System.Drawing.Image.FromFile(waterImageFile);
                int waterImageWidth = waterImage.Width;
                int waterImageHeight = waterImage.Height;

                int waterImageWidthNew = srcImageWidth - horizontalMargin * 2;
                int waterImageHeightNew = Convert.ToInt32((waterImageWidthNew * 1.0 / waterImageWidth) * waterImageHeight);

                System.Drawing.Graphics graphics = System.Drawing.Graphics.FromImage(srcImage);
                int xPos = horizontalMargin;
                int yPos = (srcImageHeight - waterImageHeightNew) / 2;

                graphics.DrawImage(waterImage, new System.Drawing.Rectangle(xPos, yPos, waterImageWidthNew, waterImageHeightNew));
                //以jpg格式保存图片
                //srcImage.Save(dstImageFile, System.Drawing.Imaging.ImageFormat.Jpeg);

                graphics.Dispose();
                waterImage.Dispose();
                //srcImage.Dispose();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
