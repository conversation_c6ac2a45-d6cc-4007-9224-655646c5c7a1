<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/cube_perface_blur">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="world" type="Matrix4f" semantic="World" />
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj" />
  <Parameter name="tex_01" type="TextureCube" semantic="Texture0" />
  <!--x, blur_radius * sqrt(2); y, 1.0/radius = 2.0 / width; z, blur_radius = tan(blur_angle * 0.5f); max blur angle is 90 degree 0.0078125,0.17633-->
  <Parameter name="tex_size_info" type="Vector4f"/>
  <Parameter name="face_normal_axis" type="Vector4f"/>
  <Parameter name="face_right_axis" type="Vector4f"/>
  <Parameter name="swizz_tranform" type="Matrix4f"/>
  
  <Sampler name="sampler_tex">
    <Texture value="tex_01" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP" />
    <AddressV value="WRAP" />
    <AddressW value="WRAP" />
  </Sampler>
  <Shader type="glsl">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			uniform mat4 world; 
			uniform mat4 vp;

			attribute vec4 a_position; 
			attribute vec2 a_uv0; 

			varying vec2 tc_tex;

			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
				tc_tex = (a_uv0.xy + vec2(-0.5, -0.5)) * vec2(2.0, -2.0);			
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
      <![CDATA[
			precision mediump float;
		
			uniform vec4 tex_size_info;
			uniform vec4 face_normal_axis;
			uniform vec4 face_right_axis;
			uniform mat4 swizz_tranform;
      
			uniform samplerCube sampler_tex;

			varying vec2 tc_tex;
      
			void main()
			{ 
        vec4 face_pos = vec4(tc_tex.x, tc_tex.y, 0, 0);
        vec3 z_dir = (face_pos * swizz_tranform).xyz + face_normal_axis.xyz;
        z_dir = normalize(z_dir);
        vec3 y_dir = cross(z_dir, face_right_axis.xyz);
        vec3 x_dir = cross(y_dir, z_dir);
        y_dir = cross(z_dir, x_dir);
        
        mat4 tangent2world = mat4(x_dir.x, x_dir.y, x_dir.z, 0.0,
                              y_dir.x, y_dir.y, y_dir.z, 0.0, 
                              z_dir.x, z_dir.y, z_dir.z, 0.0,
                              z_dir.x, z_dir.y, z_dir.z, 1.0);  

        vec4 color_acc = textureCube(sampler_tex, z_dir);
        color_acc.w = 1.0;
        vec3 local_pt = vec3(0.0, 0.0, 0.0);
        vec2 temp_val = vec2(tex_size_info.z, tex_size_info.z);
        temp_val.x = length(temp_val);
        for(local_pt.x = -tex_size_info.z; local_pt.x  < tex_size_info.z; local_pt.x  += tex_size_info.y)
        {
          for(local_pt.y = -tex_size_info.z; local_pt.y < tex_size_info.z; local_pt.y  += tex_size_info.y)
          {
            vec3 world_normal = normalize((tangent2world * vec4(local_pt, 1.0)).xyz);
            vec4 tempclr = textureCube(sampler_tex, world_normal);
            temp_val.y = (temp_val.x - length(vec2( local_pt.x, local_pt.y))) / tex_size_info.y;
            color_acc += vec4(tempclr.xyz, 1.0) * temp_val.y;
          }
        }

				gl_FragColor = color_acc / color_acc.w;
        
			}
		]]></GLShader>
  </Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};
			layout (std140) uniform material
			{
				vec4 tex_size_info;
				vec4 face_normal_axis;
				vec4 face_right_axis;
				mat4 swizz_tranform;
			};
		
			in vec4 a_position; 
			in vec2 a_uv0; 
		
  		out vec2 tc_tex;
	
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
				tc_tex = (a_uv0.xy + vec2(-0.5, -0.5)) * vec2(2.0, -2.0);			
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
        <![CDATA[
			precision mediump float;
			layout (std140) uniform material
			{
				vec4 tex_size_info;
				vec4 face_normal_axis;
				vec4 face_right_axis;
				mat4 swizz_tranform;
			};
		
			uniform samplerCube sampler_tex;
	
			in vec2 tc_tex;
			out vec4 fragColor;
	
			void main()
			{ 
        vec4 face_pos = vec4(tc_tex.x, tc_tex.y, 0, 0);
        vec3 z_dir = (face_pos * swizz_tranform).xyz + face_normal_axis.xyz;
        z_dir = normalize(z_dir);
        vec3 y_dir = cross(z_dir, face_right_axis.xyz);
        vec3 x_dir = cross(y_dir, z_dir);
        y_dir = cross(z_dir, x_dir);
        
        mat4 tangent2world = mat4(x_dir.x, x_dir.y, x_dir.z, 0.0,
                              y_dir.x, y_dir.y, y_dir.z, 0.0, 
                              z_dir.x, z_dir.y, z_dir.z, 0.0,
                              z_dir.x, z_dir.y, z_dir.z, 1.0);  

        vec4 color_acc = texture(sampler_tex, z_dir);
        color_acc.w = 1.0;
        vec3 local_pt = vec3(0.0, 0.0, 0.0);
        vec2 temp_val = vec2(tex_size_info.z, tex_size_info.z);
        temp_val.x = length(temp_val);
        for(local_pt.x = -tex_size_info.z; local_pt.x  < tex_size_info.z; local_pt.x  += tex_size_info.y)
        {
          for(local_pt.y = -tex_size_info.z; local_pt.y < tex_size_info.z; local_pt.y  += tex_size_info.y)
          {
            vec3 world_normal = normalize((tangent2world * vec4(local_pt, 1.0)).xyz);
            vec4 tempclr = texture(sampler_tex, world_normal);
            temp_val.y = (temp_val.x - length(vec2( local_pt.x, local_pt.y))) / tex_size_info.y;
            color_acc += vec4(tempclr.xyz, 1.0) * temp_val.y;
          }
        }

				fragColor = color_acc / color_acc.w;
			}
		]]></GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h" /><![CDATA[
cbuffer cbEnt : register(b0)
{
    matrix world;
};
cbuffer cbView : register(b1)
{
    matrix vp;
};
cbuffer cbMat : register(b2)
{
				float4 tex_size_info;
				float4 face_normal_axis;
				float4 face_right_axis;
				matrix swizz_tranform;
};
struct VSInput
{
    float3 pos : POSITION;
    float2 uv : TEXCOORD;
};

struct VSOutput
{
    float4 pos:SV_POSITION;
    float2 uv: TEXCOORD0;
};

SamplerState sampler_tex    : register( s0 );
TextureCube tex_01      : register( t0 );

VSOutput VsMain(VSInput input)
{
    VSOutput o;
    o.pos = mul(float4(input.pos, 1), mul(world, vp));
		o.uv = (input.uv + float2(-0.5, -0.5)) * float2(2.0, -2.0);			
    return o;
}

float4 PsMain(VSOutput input) : SV_TARGET
{
    float4 face_pos = float4(input.uv.x, input.uv.y, 0, 0);
    float3 z_dir = mul(face_pos, swizz_tranform).xyz + face_normal_axis.xyz;
    z_dir = normalize(z_dir);
    float3 y_dir = cross(z_dir, face_right_axis.xyz);
    float3 x_dir = cross(y_dir, z_dir);
    y_dir = cross(z_dir, x_dir);
         
    float4x4 tangent2world = float4x4(x_dir.x, x_dir.y, x_dir.z, 0,
                          y_dir.x, y_dir.y, y_dir.z, 0, 
                          z_dir.x, z_dir.y, z_dir.z, 0,
                          z_dir.x, z_dir.y, z_dir.z, 1);  
                            
    float4 color_acc = tex_01.Sample(sampler_tex, z_dir);
    color_acc.w = 1;
    float3 local_pt = float3(0.0, 0.0, 0.0);
    float2 temp_val = float2(tex_size_info.z, tex_size_info.z);
    temp_val.x = length(temp_val);
    for(local_pt.x = -tex_size_info.z; local_pt.x  < tex_size_info.z; local_pt.x  += tex_size_info.y)
    {
      for(local_pt.y = -tex_size_info.z; local_pt.y < tex_size_info.z; local_pt.y  += tex_size_info.y)
      {
        float3 world_normal = normalize(mul( float4(local_pt, 1),tangent2world).xyz);
        float4 tempclr = tex_01.Sample(sampler_tex, world_normal);
        temp_val.y = (temp_val.x - length(float2( local_pt.x, local_pt.y))) / tex_size_info.y;
        color_acc += float4(tempclr.xyz, 1) * temp_val.y;
      }
    }
    return color_acc / color_acc.w;
}
		]]></Shader>
  <Technique name="cube2equirect_blur">
    <Pass name="pass" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
</Effect>
