<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/ui">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="emissive" type="Colorf" default= "1,1,1,1"/>
  <Parameter name="wvp" type="Matrix4f" semantic="WorldViewProj"/>
  <!--<Parameter name="world" type="Matrix4f" semantic="World"/>-->
  <Parameter name="tex_01" type="Texture2D" semantic="Texture0"/>
  <!--<Parameter name="vec_tex" type="Vector4f" default= "1,1,0,0" />-->
  <Parameter name="clipvp" type="Matrix4f" />
  <Sampler name="sampler_tex">
    <Texture value="tex_01"/>
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR"/>
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP"/>
    <AddressV value="WRAP"/>
    <AddressW value="WRAP"/>
  </Sampler>
	<Shader type="glsl">
		<Include file="shader_buildin/gl_inc.h"/>
		<GLShader name="VsMain" type="Vertex">
			<![CDATA[			
			uniform mat4 wvp;
   		//uniform mat4 world; //
   		uniform mat4 clipvp; //
 
   		attribute vec4 a_position;
      attribute vec2 a_uv0; 
      
    	varying vec4 clip_pos; //
    	varying vec2 uv; //

   		void main() 
			{	
    		vec4 r0 = a_position * wvp;
    		//vec4 world_pos = a_position * world; //
    		clip_pos = a_position * clipvp; //
        uv = a_uv0.xy;
    		gl_Position = r0;
   		}
			]]>
		</GLShader>
		<GLShader name="PsMain" type="Pixel">
			<![CDATA[
			precision mediump float;
			uniform sampler2D sampler_tex;
      uniform vec4 emissive;
			varying vec4 clip_pos;
	    varying vec2 uv; //
	
			void main()
			{ 
				 if(abs(clip_pos.x) > 1.0 || abs(clip_pos.y) > 1.0) { discard;}
				 vec4 col_tex = texture2D(sampler_tex, uv) ;
				 gl_FragColor = col_tex * emissive;
			}
		]]>
		</GLShader>
	</Shader>
	<Shader type="hlsl5">
		<Include file="shader_buildin/dx_inc.h"/>
		<![CDATA[
   
    cbuffer cbEntity : register( b0 )
    {
    matrix wvp;   
    //matrix world;
    };
    
    cbuffer cbMat : register( b1 )
    {
    matrix clipvp;
    float4 emissive;
    };
    
		SamplerState sampler_tex : register( s0 );
    Texture2D tex_01 : register( t0 );
  
		struct VsInput
		{
			float3     pos:POSITION;
			float2     tc :TEXCOORD; 
		};
    
		struct VsOutput
		{
			float4  pos             :   SV_Position;
			float2  tc_tex          :   TEXCOORD0;
      float4  clip_pos        :   TEXCOORD1;  
		};
    
		VsOutput VsMain(VsInput input)
		{
			VsOutput o = (VsOutput)0;	
			o.pos = mul(float4(input.pos,1),wvp);
      o.tc_tex = input.tc;
      o.clip_pos = mul(float4(input.pos,1),clipvp);	
			return o;
		}
    
		float4 PsMain(VsOutput input) : SV_Target
		{
      clip(float2(1, 1) - abs(input.clip_pos.xy));
			float4 clr = tex_01.Sample(sampler_tex, input.tc_tex.xy);
			return clr * emissive;
		}
		]]>
		</Shader>
	<Technique name="tech">
		<Pass name="pass" restorestate="false">
			<State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0"/>
			<State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0"/>
		</Pass>
	</Technique>
</Effect>