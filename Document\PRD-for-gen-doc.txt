我是一个软件工程师，我需要编写软件设计文档。
目前我有一个现成的软件及其代码，我如何使用AI工具协助我编写这个软件的概要设计、数据库设计、详细设计文档，
我的期望效果是由AI工具完成文档初稿，我手工进行审核修改。
请推荐具体的方案以及我需要提供的数据，以及需要做什么资料准备或者需要提供什么工具。
请先不要开始输出设计文件，我们先对方案达成一致后，我们再开始分析代码，输出文档等。
已知我目前能提供：
1、源代码，见文件夹@E:\AITest\EAIDoc\Augment\PicWorkStationWindowsFormsForALi_rili。
2、软件功能概述：
主要实现了工作站的界面，任务处理相关操作，包括接收任务，处理任务，修改任务状态，任务完成后回调等。
3、软件开发使用的技术：
.NET技术
4、没有直接使用数据库，是使用其他组件的API获取数据，可以不做数据库设计。
5、设计文档的内容结构，可以按照业界通用结构，在我们得到初稿以后，可以沟通进行调整。
文档格式期望是word格式的文档，文档的详细程度期望面向开发人员的技术文档，越详细越好。
6、软件有配置文件，也有外部依赖的API，这个软件只是我们一个应用系统的一个子模块，
所以它可能会调用其他组件的API，请在代码分析时确定。外部依赖的API只需要提供调用是的输入和输出说明接口，不需要分析或反编译其内部功能。
7、没有特别需要关注的模块，做成一致的详细程度。
8、详细设计文档的要求：
1）遍历所有目录，做详细代码分析。
2）需要含类图、时序图，部分比较复杂的方法需要放伪代码。
3）每个类包含属性定义和描述，每个类的方法需要包含方法参数的定义和说明，方法体的逻辑说明。
9、概要设计、详细设计保存到文件夹@Document下，同时生成一份文档使用说明，对这些设计文档如何使用进行指南。
10、请根据你对代码的分析，额外生成一份项目分析报告，除了报告相关代码分析信息，还可以包括你对代码设计质量的评价及优化建议。
