﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.ServiceModel.Configuration;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Xml;
using System.Xml.Linq;

namespace Datagrid
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        public MainWindow()
        {
            InitializeComponent();
            DataContext = this;
        }

        public void updateCADTaskClientConfigXML()
        {
            string xmlPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "CADTaskClientConfig.xml");
            XmlDocument doc = new XmlDocument();
            doc.Load(xmlPath);

            XmlNode workPathNode = doc.SelectSingleNode("/CADTaskClientConfig/WorkPath");
            workPathNode.InnerText = WorkPath;

            if (WsPort > 0)
            {
                XmlNode cadServerNode = doc.SelectSingleNode("/CADTaskClientConfig/WCFCADServer");
                Uri cadUri = new Uri(cadServerNode.InnerText);
                cadServerNode.InnerText = cadServerNode.InnerText.Replace(cadUri.Port.ToString(), WsPort.ToString());
            }

            XmlNode edsServerNode = doc.SelectSingleNode("/CADTaskClientConfig/WCFEdsServer");
            Uri edsUri = new Uri(edsServerNode.InnerText);
            edsServerNode.InnerText = edsServerNode.InnerText.Replace(edsUri.Host, ecsIPAddress);

            XmlNode sendbackUrlNode = doc.SelectSingleNode("/CADTaskClientConfig/SendbackUrl");
            Uri ecsUri = new Uri(sendbackUrlNode.InnerText);
            sendbackUrlNode.InnerText = sendbackUrlNode.InnerText.Replace(ecsUri.Host, ecsIPAddress);

            XmlNode egiServiceNode = doc.SelectSingleNode("/CADTaskClientConfig/EGIService");
            Uri egiUri = new Uri(egiServiceNode.InnerText);
            egiServiceNode.InnerText = egiServiceNode.InnerText.Replace(egiUri.Host, ecsIPAddress);

            doc.Save(xmlPath);
        }

        public void updateSDSServerConfig()
        {
            string configPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PicWorkStationWindowsFormsForALi.exe.config");
            ExeConfigurationFileMap fileMap = new ExeConfigurationFileMap { ExeConfigFilename = configPath };
            Configuration config = ConfigurationManager.OpenMappedExeConfiguration(fileMap, ConfigurationUserLevel.None);

            string dataServer = config.AppSettings.Settings["DataServer"].Value;
            Uri dataUri = new Uri(dataServer);
            config.AppSettings.Settings["DataServer"].Value = dataServer.Replace(dataUri.Host, SDSIPAddress);

            string fileServer = config.AppSettings.Settings["FileServer"].Value;
            Uri fileUri = new Uri(fileServer);
            config.AppSettings.Settings["FileServer"].Value = fileServer.Replace(fileUri.Host, SDSIPAddress);

            string ecsServer = config.AppSettings.Settings["EcsServer"].Value;
            Uri ecsUri = new Uri(ecsServer);
            config.AppSettings.Settings["EcsServer"].Value = ecsServer.Replace(ecsUri.Host, ECSIPAddress);

            config.Save(ConfigurationSaveMode.Modified);
        }

        public void updatePdsCadWsOutsideControlXml()
        {
            string xmlPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PdsCadWsOutsideControl.xml");
            if (!File.Exists(xmlPath)) return;

            XmlDocument doc = new XmlDocument();
            doc.Load(xmlPath);
                
            XmlNode proeProcessNameNode = doc.SelectSingleNode("/CadServerConfig/ProeProcessName");
            proeProcessNameNode.InnerText = "PicWorkStationWindowsFormsForALi";
            XmlNode proEStartPathNode = doc.SelectSingleNode("/CadServerConfig/ProEStartPath");
            proEStartPathNode.InnerText = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, 
                "PicWorkStationWindowsFormsForALi.exe");;

            doc.Save(xmlPath);
        }

        public void updatePdsCadWsOutsideControlConfig()
        {
            string configPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Zxtech.PdsCadWsOutsideControl.exe.config");
            if (!File.Exists(configPath)) return;

            ExeConfigurationFileMap fileMap = new ExeConfigurationFileMap { ExeConfigFilename = configPath };
            Configuration config = ConfigurationManager.OpenMappedExeConfiguration(fileMap, ConfigurationUserLevel.None);

            config.AppSettings.Settings["AppName"].Value = DemonsName;

            ServicesSection servicesSection = config.GetSection("system.serviceModel/services") as ServicesSection;
            foreach (ServiceElement service in servicesSection.Services)
            {
                foreach (BaseAddressElement baseAddress in service.Host.BaseAddresses)
                {
                    Uri uri = new Uri(baseAddress.BaseAddress);
                    baseAddress.BaseAddress = baseAddress.BaseAddress.Replace(uri.Port.ToString(), DemonsPort.ToString());
                }
            }

            config.Save(ConfigurationSaveMode.Modified);
        }

        public void updateRabbiMQConfig()
        {
            string configPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "RabbiMQ", "Zxtech.CAD.MQListener.exe.config");
            if (!File.Exists(configPath)) return;

            ExeConfigurationFileMap fileMap = new ExeConfigurationFileMap { ExeConfigFilename = configPath };
            Configuration config = ConfigurationManager.OpenMappedExeConfiguration(fileMap, ConfigurationUserLevel.None);

            config.AppSettings.Settings["mq_hostname"].Value = this.MQIPAddress;
            config.AppSettings.Settings["mq_port"].Value = this.MQPort.ToString();
            config.AppSettings.Settings["mq_username"].Value = this.MQUserName;
            config.AppSettings.Settings["mq_password"].Value = this.MQUserPassword;
            config.AppSettings.Settings["mq_name"].Value = this.MQQueueName;
            config.AppSettings.Settings["mq_ssl"].Value = this.MQSSL.ToString().ToLower();
            config.AppSettings.Settings["AppPath"].Value = this.AppPath;
            
            config.Save(ConfigurationSaveMode.Modified);
        }

        //蓝图服务器地址
        private string sdsIPAddress;
        public string SDSIPAddress
        {
            get { return sdsIPAddress; }
            set
            {
                if (sdsIPAddress != value)
                {
                    sdsIPAddress = value;
                    OnPropertyChanged("SDSIPAddress");
                }
            }
        }
        //ecs服务器地址
        private string ecsIPAddress;
        public string ECSIPAddress
        {
            get { return ecsIPAddress; }
            set
            {
                if (ecsIPAddress != value)
                {
                    ecsIPAddress = value;
                    OnPropertyChanged("ECSIPAddress");
                }
            }
        }
        //输出成果物存放目录
        private string workPath;
        public string WorkPath
        {
            get { return workPath; }
            set
            {
                if (workPath != value)
                {
                    workPath = value;
                    OnPropertyChanged("WorkPath");
                }
            }
        }
        //守护程序名字
        private string demonsName;
        public string DemonsName
        {
            get { return demonsName; }
            set
            {
                if (demonsName != value)
                {
                    demonsName = value;
                    OnPropertyChanged("DemonsName");
                }
            }
        }
        //守护程序监听的端口
        private int demonsPort;
        public int DemonsPort
        {
            get { return demonsPort; }
            set
            {
                if (demonsPort != value)
                {
                    demonsPort = value;
                    OnPropertyChanged("DemonsPort");
                }
            }
        }

        //工作站服务端口
        private int wsPort;
        public int WsPort
        {
            get { return wsPort; }
            set
            {
                if (wsPort != value)
                {
                    wsPort = value;
                    OnPropertyChanged("WsPort");
                }
            }
        }

        //MQ服务器IP地址
        private string mqIPAddress;
        public string MQIPAddress
        {
            get { return mqIPAddress; }
            set
            {
                if(mqIPAddress != value)
                {
                    mqIPAddress = value;
                    OnPropertyChanged("MQIPAddress");
                }
            }
        }

        //MQ服务器端口
        private int mqPort;
        public int MQPort
        {
            get { return mqPort; }
            set
            {
                if (mqPort != value)
                {
                    mqPort = value;
                    OnPropertyChanged("MQPort");
                }
            }
        }

        //MQ服务器登录用户名
        private string mqUserName;
        public string MQUserName
        {
            get { return mqUserName; }
            set
            {
                if (mqUserName != value)
                {
                    mqUserName = value;
                    OnPropertyChanged("MQUserName");
                }
            }
        }

        //MQ服务器登录用户密码
        private string mqUserPassword;
        public string MQUserPassword
        {
            get { return mqUserPassword; }
            set
            {
                if (mqUserPassword != value)
                {
                    mqUserPassword = value;
                    OnPropertyChanged("MQUserPassword");
                }
            }
        }

        //MQ队列名称
        private string mqQueueName;
        public string MQQueueName
        {
            get { return mqQueueName; }
            set
            {
                if (mqQueueName != value)
                {
                    mqQueueName = value;
                    OnPropertyChanged("MQQueueName");
                }
            }
        }

        //SSL
        private bool mqSSL;
        public bool MQSSL
        {
            get { return mqSSL; }
            set
            {
                if (mqSSL != value)
                {
                    mqSSL = value;
                    OnPropertyChanged("MQSSL");
                }
            }
        }

        //工作站安装路径
        private string appPath;
        public string AppPath
        {
            get { return appPath; }
            set
            {
                if (appPath != value)
                {
                    appPath = value;
                    OnPropertyChanged("AppPath");
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string s)
        {
            var handler = PropertyChanged;
            if (handler != null)
            {
                handler(this, new PropertyChangedEventArgs(s));
            }
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ValidateIPAddress(SDSIPAddress)
                    && ValidateIPAddress(ECSIPAddress) 
                    //&& DemonsPort > 0 
                    && WsPort > 0 
                    && DemonsPort != WsPort
                    /*&& !string.IsNullOrWhiteSpace(WorkPath)
                    && !string.IsNullOrWhiteSpace(DemonsName)*/
                    && ValidateIPAddress(MQIPAddress)
                    && MQPort > 0
                    && !string.IsNullOrWhiteSpace(MQUserName)
                    && !string.IsNullOrWhiteSpace(MQUserPassword)
                    && !string.IsNullOrWhiteSpace(MQQueueName)
                    && !string.IsNullOrWhiteSpace(AppPath)
                    && File.Exists(AppPath))
                {
                    updateSDSServerConfig();
                    updateCADTaskClientConfigXML();
                    updatePdsCadWsOutsideControlConfig();
                    updatePdsCadWsOutsideControlXml();
                    updateRabbiMQConfig();

                    Configuration config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                    config.AppSettings.Settings["SDSIPAddressVal"].Value = this.SDSIPAddress;
                    config.AppSettings.Settings["ECSIPAddressVal"].Value = this.ECSIPAddress;
                    config.AppSettings.Settings["WsPortVal"].Value = this.WsPort.ToString();
                    config.AppSettings.Settings["WorkPathVal"].Value = this.WorkPath;
                    config.AppSettings.Settings["DemonsNameVal"].Value = this.DemonsName;
                    config.AppSettings.Settings["DemonsPortVal"].Value = this.DemonsPort.ToString();

                    config.AppSettings.Settings["MQIPAddressVal"].Value = this.MQIPAddress;
                    config.AppSettings.Settings["MQPortVal"].Value = this.MQPort.ToString();
                    config.AppSettings.Settings["MQUserNameVal"].Value = this.MQUserName;
                    config.AppSettings.Settings["MQUserPasswordVal"].Value = this.MQUserPassword;
                    config.AppSettings.Settings["MQQueueNameVal"].Value = this.MQQueueName;
                    config.AppSettings.Settings["MQSSLVal"].Value = this.MQSSL.ToString().ToLower();
                    config.AppSettings.Settings["AppPathVal"].Value = this.AppPath;

                    config.Save(ConfigurationSaveMode.Modified);
                    ConfigurationManager.RefreshSection("appSettings");

                    this.Close();
                }
                else
                {
                    MessageBox.Show(ConfigurationManager.AppSettings["ErrorMsg"]);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private bool ValidateIPAddress(string ipAddress)
        {
            Regex validipregex=new Regex(@"^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$");
            if (ipAddress != "" && validipregex.IsMatch(ipAddress.Trim()))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            var settings = ConfigurationManager.AppSettings;
            this.Title = settings["Title"];
            this.sdsIPTextBlock.Text = settings["SDSIPAddress"];
            this.ecsIPTextBlock.Text = settings["ECSIPAddress"];
            this.workPathTextBlock.Text = settings["WorkPath"];
            this.workPathTipTextBlock.Text = settings["WorkPathTip"];
            this.demonsNameTextBlock.Text = settings["DemonsName"];
            this.demonsNameTipTextBlock.Text = settings["DemonsNameTip"];
            this.demonsPortTextBlock.Text = settings["DemonsPort"];
            this.demonsPortTipTextBlock.Text = settings["DemonsPortTip"];
            this.wsPortTextBlock.Text = settings["WsPort"];
            this.wsPortTipTextBlock.Text = settings["WsPortTip"];
            this.okButton.Content = settings["OKButton"];

            MQServerIPAddressTextBlock.Text = settings["MQIPAddress"];
            MQServerPortTextBlock.Text = settings["MQPort"];
            MQUserNameTextBlock.Text = settings["MQUserName"];
            MQUserPasswordTextBlock.Text = settings["MQUserPassword"];
            MQQueueTextBlock.Text = settings["MQQueueName"];
            MQSSLCheckBox.Content = settings["MQSSL"];
            PicWorkStationPathTextBlock.Text = settings["AppPath"];

            //
            this.SDSIPAddress = settings["SDSIPAddressVal"];
            this.ECSIPAddress = settings["ECSIPAddressVal"];

            var wsPortStr = settings["WsPortVal"];
            int wsPort;
            int.TryParse(wsPortStr, out wsPort);
            this.WsPort = wsPort;

            this.WorkPath = settings["WorkPathVal"];
            this.DemonsName = settings["DemonsNameVal"];

            var demonsPortStr = settings["DemonsPortVal"];
            int demonsPort;
            int.TryParse(demonsPortStr, out demonsPort);
            this.DemonsPort = demonsPort;

            this.MQIPAddress = settings["MQIPAddressVal"];

            var mqPortVal = settings["MQPortVal"];
            if (!string.IsNullOrWhiteSpace(mqPortVal))
            {
                int mqPortV;
                int.TryParse(mqPortVal, out mqPortV);
                this.MQPort = mqPortV;
            }

            this.MQUserName = settings["MQUserNameVal"];
            this.MQUserPassword = settings["MQUserPasswordVal"];
            this.MQQueueName = settings["MQQueueNameVal"];
            var mqssl = settings["MQSSLVal"];
            if (!string.IsNullOrWhiteSpace(mqssl))
            {
                bool mqsslV;
                bool.TryParse(mqssl, out mqsslV);
                this.MQSSL = mqsslV;
            }
            
            this.AppPath = settings["AppPathVal"];
        }
    }
}
