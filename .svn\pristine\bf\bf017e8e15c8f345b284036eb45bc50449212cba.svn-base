#define TRANSFORM_TEXCOORD(tex,trans) (tex.xy * trans.xy + trans.zw)
#define ROTATE_TEXCOORD(tex,trans) (float2(dot(tex.xy, trans.xy), dot(tex.xy, trans.zw)))

#define MUL(w, vp) (mul(w, vp))

#define RECIPROCAL_PI2 0.15915494
#define RECIPROCAL_PI 0.31830988618

float4 PlanarMapping(float4 world_pos, float4x4 ref_inv_world_mat)
{
	float4 pos = mul(world_pos, ref_inv_world_mat);
	pos /= pos.w;
	return pos * float4(1, -1, 0, 0) + float4(0.5, 0.5, 0, 0);
}

float4 ScreenMapping(float4 wvp_pos)
{
	wvp_pos /= wvp_pos.w;
	return wvp_pos * float4(0.5, -0.5, 0, 0) + float4(0.5, 0.5, 0, 0);
}

#define VS_COMPUTE_REFLECT_UV  if (setting.x > 0) {if (setting.y > 0) {float4x4 wvp1 = mul(MUL(world, v), tp);o.tc_ref = mul(float4(input.pos, 1), wvp1);}else {o.tc_ref.xy = TRANSFORM_TEXCOORD(input.tc.xy, vec_ref); }}else {o.tc_ref.xy = TRANSFORM_TEXCOORD(input.tc.xy, vec_ref);o.tc_ref.x = 1 - o.tc_ref.x; }

float4 ComputeReflectUVInVS(float4 setting, float4x4 world, float4x4 v, float4x4 tp, 
	float3 input_pos, float2 input_tc, float4 vec_ref)
{
	float4 tc_ref;
	if (setting.x > 0) 
	{ 
		if (setting.y > 0)
		{ 
			float4x4 wvp1 = mul(MUL(world, v), tp); 
			tc_ref = mul(float4(input_pos, 1), wvp1);
		} 
		else
		{
			tc_ref.xy = TRANSFORM_TEXCOORD(input_tc, vec_ref);
		}
	}
	else 
	{ 
		tc_ref.xy = TRANSFORM_TEXCOORD(input_tc, vec_ref);
		tc_ref.x = 1 - tc_ref.x;
	}

	return tc_ref;
}

#define PS_COMPUTE_REFLECT float4 col_ref;if (setting.y > 0) {float4 r1 = ScreenMapping(input.tc_ref);col_ref = tex_ref.Sample(sampler_ref, r1.xy);}else {col_ref = tex_ref.Sample(sampler_ref, input.tc_ref.xy);}

float4 ComputeReflectColorInPS(SamplerState sampler_ref, Texture2D tex_ref, float4 tc_ref, float4 setting)
{
	float4 col_ref;
	if (setting.y > 0)
	{ 
		float4 r1 = ScreenMapping(tc_ref);
		col_ref = tex_ref.Sample(sampler_ref, r1.xy);
	}
	else 
	{ 
		col_ref = tex_ref.Sample(sampler_ref, tc_ref.xy);
	}
	return col_ref;
}

#define VS_COMPUTE_CLIP o.tc_tex.z = 0;if (setting.x < 0 && is_clip > 0){o.tc_tex.z = 1;float4 world_pos = mul(float4(input.pos, 1), world);o.tc_tex.w = dot(world_pos, clip_plane);}

void ComputeClipInVS(inout float4 tc_tex, float3 input_pos, float4 setting, float is_clip, float4 clip_plane, float4x4 world)
{
	tc_tex.z = 0;
	if (setting.x < 0 && is_clip > 0)
	{ 
		tc_tex.z = 1;
		float4 world_pos = mul(float4(input_pos, 1), world);
		tc_tex.w = dot(world_pos, clip_plane);
	}
}

#define PS_COMPUTE_CLIP  if(input.tc_tex.z > 0)  { clip(input.tc_tex.w); }

float2 ConvertCubeReflectVector2EquirectangleUV(float3 reflect)
{
	float2 ret;
	ret.y = acos(reflect.y);
	ret.x = atan2(reflect.z, reflect.x);
	ret.xy = ret.xy * float2(RECIPROCAL_PI2, RECIPROCAL_PI) + float2(0.25, 0);
	return ret;
}

void ComputeProbeBoxWorldInfo(
	//three out parameter 
	out float4 cwp_box_pos, out float3 ref_normal, out float4 ref_pos,
	//six in parameter
	float4x4 world, float4x4 inv_world_transpose, float4x4 to_box_world_trans,
	float3 local_pos, float3 cam_pos_world, float3 normal)
{
	//trasform vertex position from model space to box space via world space
	ref_pos = mul(float4(local_pos, 1.0), world);
	ref_pos = mul(ref_pos, to_box_world_trans);

	//trasform camera position from world space to box space
	cwp_box_pos = mul(float4(cam_pos_world, 1.0), to_box_world_trans);

	//transform vertex normal from model space to box space via world space
	ref_normal = mul(normal, (float3x3)inv_world_transpose);
	ref_normal = mul(ref_normal, (float3x3)to_box_world_trans);
	ref_normal = normalize(ref_normal);
}

float4 EquirectEnvMapping(SamplerState s, Texture2D tex, float3 vertexPos, float3 origUV, float4 cubeMin, float4 cubeMax, float4 cubePos)
{
	if (cubePos.w > 0)
	{
		float3 invOrigUV = 1.0f / origUV;
		float3 intersecAtMaxPlane = (cubeMax.xyz - vertexPos) * invOrigUV;
		float3 intersecAtMinPlane = (cubeMin.xyz - vertexPos) * invOrigUV;
		// Get the largest intersection values (we are not intersted in negative values)
		float3 largestIntersec = max(intersecAtMaxPlane, intersecAtMinPlane);
		// Get the closest of all solutions
		float distance = min(min(largestIntersec.x, largestIntersec.y), largestIntersec.z);
		// Get the intersection position
		float3 intersectPositionWS = vertexPos + origUV * distance;
		// Get corrected vector
		origUV = normalize(intersectPositionWS - cubePos.xyz);

		float2 uv = ConvertCubeReflectVector2EquirectangleUV(origUV);
		return tex.Sample(s, uv.xy);
	}
	else
	{
		float2 uv = ConvertCubeReflectVector2EquirectangleUV(origUV);
		return tex.Sample(s, uv.xy);
	}
}

float4 EnvMapping(SamplerState s, TextureCube tex, float3 vertexPos, float3 origUV, float4 cubeMin, float4 cubeMax, float4 cubePos)
{
	// Find the ray intersection with box plane
	if (cubePos.w > 0)
	{
		float3 invOrigUV = 1.0f / origUV;
		float3 intersecAtMaxPlane = (cubeMax.xyz - vertexPos) * invOrigUV;
		float3 intersecAtMinPlane = (cubeMin.xyz - vertexPos) * invOrigUV;
		// Get the largest intersection values (we are not intersted in negative values)
		float3 largestIntersec = max(intersecAtMaxPlane, intersecAtMinPlane);
		// Get the closest of all solutions
		float distance = min(min(largestIntersec.x, largestIntersec.y), largestIntersec.z);
		// Get the intersection position
		float3 intersectPositionWS = vertexPos + origUV * distance;
		// Get corrected vector
		origUV = normalize(intersectPositionWS - cubePos.xyz);

		return tex.SampleLevel(s, origUV, 0);
	}
	else
	{
		return tex.Sample(s, origUV);
	}
}
//
//inline float3 reflectProbeCorrectNormal2(float3 vertexPos, float3 origUV, float3 cubePos, float3 cubeMin, float3 cubeMax)
//{
//	//float3 nrdir = normalize(origUV);
//	float3 invOrigUV = 1.0f / origUV;
//#if 0
//	float3 rbmax = (cubeMax - vertexPos) * invOrigUV;
//	float3 rbmin = (cubeMin - vertexPos) * invOrigUV;
//
//	float3 rbminmax = (origUV > 0.0f) ? rbmax : rbmin;
//
//#else // Optimized version
//	float3 rbmax = (cubeMax - vertexPos);
//	float3 rbmin = (cubeMin - vertexPos);
//
//	float3 select = step(float3(0, 0, 0), origUV);
//	float3 rbminmax = lerp(rbmax, rbmin, select);
//	rbminmax *= invOrigUV;
//#endif
//
//	float fa = min(min(rbminmax.x, rbminmax.y), rbminmax.z);
//
//	vertexPos -= cubePos;
//	origUV = vertexPos + origUV * fa;
//
//	return origUV;
//}

