<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/texture_reverse_y">
  <Parameter name="tex01" type="Texture2D" semantic="Texture0"/>
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj"/>>
  <Parameter name="world" type="Matrix4f" semantic="World"/>
  <Parameter name="emissive" type="Colorf" default= "1,1,1,1"/>
  <Parameter name="is_clip" type="float32" default="0" semantic="EffectGlobal" />
  <Parameter name="clip_plane" type="Vector4f" default="0,1,0,0" semantic="EffectGlobal" />
  <Sampler name="sampler01">
		<Texture value="tex01"/>
		<Filter value="MIN_MAG_MIP_LINEAR"/>
		<AddressU value="WRAP"/>
		<AddressV value="WRAP"/>
		<AddressW value="WRAP"/>
	</Sampler>
	<Shader type="glsl">
    <Include file="shader_buildin/gl_inc.h"/>
    <GLShader name="VsMain" type="Vertex">
			<![CDATA[
      precision highp float;
			uniform mat4 vp; 
      uniform mat4 world;
						
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 

			varying vec4 uv;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				uv.xy = a_uv0.xy;
			}
			]]>
		</GLShader>
		<GLShader name="PsMain" type="Pixel">
			<![CDATA[
			precision mediump float;
						
			uniform sampler2D sampler01;
			uniform vec4 emissive;
			varying vec4 uv;
	
			void main()
			{
				gl_FragColor = texture2D(sampler01, vec2(uv.x, 1.0-uv.y)) * emissive;
			}
		]]>
		</GLShader>
	</Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h"/>
    <GLShader name="VsMain" type="Vertex">
      <![CDATA[
      precision highp float;
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};
						
			in vec4 a_position; 
			in vec2 a_uv0; 

			out vec4 uv;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				uv.xy = a_uv0.xy;
			}
			]]>
    </GLShader>
    <GLShader name="PsMain" type="Pixel">
      <![CDATA[
			precision mediump float;
						
			uniform sampler2D sampler01;
			layout (std140) uniform mat
			{
				vec4 emissive;
			};
			in vec4 uv;
			out vec4 fragColor;
	
			void main()
			{
				fragColor = texture(sampler01, vec2(uv.x, 1.0-uv.y)) * emissive;
			}
		]]>
    </GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h"/>
    <![CDATA[
    cbuffer cbEntity : register( b0 )
    {
    matrix world;
    };
    cbuffer cbEntity : register( b3 )
    {
    matrix vp;
    };

    cbuffer cbMaterial : register( b1 )
    {  
		float4 emissive;
    };
        
    cbuffer cbGlobal : register( b2 )
    {
    float4 clip_plane;
    float is_clip;
    };
        
    SamplerState sampler01 : register( s0 );
    Texture2D tex01 : register( t0 );

		struct VsInput
		{
			float3 pos : POSITION;
			float2 uv : TEXCOORD0;
		};
        
		struct VsOutput
		{
			float4 pos : SV_POSITION;
			float4 uv : TEXCOORD0;
		};
    
		VsOutput VsMain(VsInput input)
		{
			VsOutput o = (VsOutput)0;
			o.pos = mul(float4(input.pos, 1), MUL(world, vp));
			o.uv.xy = input.uv;
      
			if(is_clip > 0)
			{
			float4 world_pos = mul(float4(input.pos, 1), world);
			o.uv.w = dot(world_pos, clip_plane);
			}
      
			return o;
		}
    
		float4 PsMain(VsOutput input) : SV_Target
		{	 
			if(is_clip > 0)  { clip(input.uv.w); }
			float4 clr = tex01.Sample(sampler01, input.uv.xy);
			return float4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a); //ModulateAlpha C = Cs*Cd, A = As*Ad
		}
		]]>
	</Shader>

	<Technique name="TextureDiffuse">
		<Pass name="pass1" restorestate="false">
			<State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0"/>
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0"/>
		</Pass>
	</Technique>
</Effect>

