﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2013
VisualStudioVersion = 12.0.40629.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PicWorkStationWindowsFormsForALi_XiZi", "PicWorkStationWindowsFormsForALi_XiZi.csproj", "{B4E6F690-D09D-4E2F-BC1D-535A95D4726D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zxtech.Vr.OutReport.ExcelLibrary", "Zxtech.Vr.OutReport.Excel\Zxtech.Vr.OutReport.ExcelLibrary.csproj", "{10DFD9F4-D54E-43FA-BB6A-54FB5E7D952F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B4E6F690-D09D-4E2F-BC1D-535A95D4726D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4E6F690-D09D-4E2F-BC1D-535A95D4726D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4E6F690-D09D-4E2F-BC1D-535A95D4726D}.Debug|x86.ActiveCfg = Debug|x86
		{B4E6F690-D09D-4E2F-BC1D-535A95D4726D}.Debug|x86.Build.0 = Debug|x86
		{B4E6F690-D09D-4E2F-BC1D-535A95D4726D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4E6F690-D09D-4E2F-BC1D-535A95D4726D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4E6F690-D09D-4E2F-BC1D-535A95D4726D}.Release|x86.ActiveCfg = Release|x86
		{B4E6F690-D09D-4E2F-BC1D-535A95D4726D}.Release|x86.Build.0 = Release|x86
		{10DFD9F4-D54E-43FA-BB6A-54FB5E7D952F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{10DFD9F4-D54E-43FA-BB6A-54FB5E7D952F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{10DFD9F4-D54E-43FA-BB6A-54FB5E7D952F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{10DFD9F4-D54E-43FA-BB6A-54FB5E7D952F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{10DFD9F4-D54E-43FA-BB6A-54FB5E7D952F}.Release|Any CPU.Build.0 = Release|Any CPU
		{10DFD9F4-D54E-43FA-BB6A-54FB5E7D952F}.Release|x86.ActiveCfg = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
