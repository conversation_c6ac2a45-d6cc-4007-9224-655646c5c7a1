﻿<Window x:Class="Datagrid.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Configuration" Height="590" Width="480" WindowStartupLocation="CenterScreen" Topmost="True"
        ResizeMode="NoResize" Loaded="Window_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid HorizontalAlignment="Center" VerticalAlignment="Center">
            <StackPanel Orientation="Vertical" Width="380">
                <StackPanel Orientation="Vertical">
                    <TextBlock x:Name="sdsIPTextBlock" Text="SDSIPAddress"></TextBlock>
                    <TextBox Height="30" VerticalContentAlignment="Center" Text="{Binding SDSIPAddress, Mode=TwoWay}"></TextBox>
                </StackPanel>
                <StackPanel Orientation="Vertical" Margin="0,16,0,0" >
                    <TextBlock x:Name="ecsIPTextBlock" Text="ECSIPAddress"></TextBlock>
                    <TextBox Height="30" VerticalContentAlignment="Center" Text="{Binding ECSIPAddress, Mode=TwoWay}"></TextBox>
                </StackPanel>
                <StackPanel Orientation="Vertical" Margin="0,16,0,0" >
                    <TextBlock x:Name="wsPortTextBlock" Text="WsPort"></TextBlock>
                    <TextBox Height="30" VerticalContentAlignment="Center" Text="{Binding WsPort, Mode=TwoWay}"></TextBox>
                    <TextBlock x:Name="wsPortTipTextBlock" Text="" FontSize="10"></TextBlock>
                </StackPanel>
                <StackPanel Orientation="Vertical" Margin="0,6,0,0">
                    <TextBlock x:Name="workPathTextBlock" Text="WorkPath"></TextBlock>
                    <TextBox Height="30" VerticalContentAlignment="Center" Text="{Binding WorkPath, Mode=TwoWay}"></TextBox>
                    <TextBlock x:Name="workPathTipTextBlock" Text="" FontSize="10"></TextBlock>
                </StackPanel>
                <StackPanel Orientation="Vertical" Margin="0,6,0,0" Visibility="Collapsed">
                    <TextBlock x:Name="demonsNameTextBlock" Text="DemonsName"></TextBlock>
                    <TextBox Height="30" VerticalContentAlignment="Center" Text="{Binding DemonsName, Mode=TwoWay}"></TextBox>
                    <TextBlock x:Name="demonsNameTipTextBlock" Text="" FontSize="10"></TextBlock>
                </StackPanel>
                <StackPanel Orientation="Vertical" Margin="0,6,0,0" Visibility="Collapsed">
                    <TextBlock x:Name="demonsPortTextBlock" Text="DemonsPort"></TextBlock>
                    <TextBox Height="30" VerticalContentAlignment="Center" Text="{Binding DemonsPort, Mode=TwoWay}"></TextBox>
                    <TextBlock x:Name="demonsPortTipTextBlock" Text="" FontSize="10"></TextBlock>
                </StackPanel>
            </StackPanel>
        </Grid>
        <Grid Grid.Row="1">
            <Grid HorizontalAlignment="Center" VerticalAlignment="Center" Width="380">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBlock x:Name="MQServerIPAddressTextBlock" Margin="0,15,0,0" Text="MQServerIPAddress"/>
                <TextBox Grid.Row="1" Grid.Column="0" Height="30" Margin="0,0,3,0" VerticalContentAlignment="Center" Text="{Binding MQIPAddress, Mode=TwoWay}"></TextBox>
                <TextBlock x:Name="MQServerPortTextBlock" Grid.Column="1" Margin="3,15,0,0" Text="MQServerPort"/>
                <TextBox Grid.Row="1" Grid.Column="1" Height="30" Margin="3,0,0,0" VerticalContentAlignment="Center" Text="{Binding MQPort, Mode=TwoWay}"></TextBox>
                <TextBlock x:Name="MQUserNameTextBlock" Grid.Row="2" Grid.Column="0" Margin="0,15,0,0" Text="MQUserName"/>
                <TextBox Grid.Row="3" Grid.Column="0" Height="30" Margin="0,0,3,0" VerticalContentAlignment="Center" Text="{Binding MQUserName, Mode=TwoWay}"></TextBox>
                <TextBlock x:Name="MQUserPasswordTextBlock" Grid.Row="2" Grid.Column="1" Margin="3,15,0,0" Text="MQUserPassword"/>
                <TextBox Grid.Row="3" Grid.Column="1" Height="30" Margin="3,0,0,0" VerticalContentAlignment="Center" Text="{Binding MQUserPassword, Mode=TwoWay}"></TextBox>
                <TextBlock x:Name="MQQueueTextBlock" Grid.Row="4" Grid.Column="0" Margin="0,15,0,0" Text="MQQueueName"/>
                <TextBox Grid.Row="5" Grid.Column="0" Height="30" Margin="0,0,3,0" VerticalContentAlignment="Center" Text="{Binding MQQueueName, Mode=TwoWay}"></TextBox>
                <CheckBox x:Name="MQSSLCheckBox" Grid.Row="5" Grid.Column="1" Margin="3,5,0,0" Content="SSL" IsChecked="{Binding MQSSL, Mode=TwoWay}"/>
                <TextBlock x:Name="PicWorkStationPathTextBlock" Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,15,0,0" Text="PicWorkStationPath"/>
                <TextBox Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="2" Height="30" Margin="0,0,3,0" VerticalContentAlignment="Center" Text="{Binding AppPath, Mode=TwoWay}"></TextBox>
            </Grid>
        </Grid>
        <Button x:Name="okButton" Grid.Row="2" Height="30" Width="380" Margin="0,20,0,0" Click="Button_Click">OK</Button>
    </Grid>
</Window>
