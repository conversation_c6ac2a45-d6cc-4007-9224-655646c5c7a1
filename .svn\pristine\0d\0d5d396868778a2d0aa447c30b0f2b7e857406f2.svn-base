﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <add key="Title" value="Configuration" />
    <add key="SDSIPAddress" value="蓝图服务器IP地址" />
    <add key="ECSIPAddress" value="ECS服务器IP地址" />
    <add key="WorkPath" value="共享文件夹" />
    <add key="WorkPathTip" value="请确认共享文件夹可以访问" />
    <add key="DemonsName" value="守护程序名字" />
    <add key="DemonsNameTip" value="不同的实例请使用不同的名字" />
    <add key="DemonsPort" value="守护程序端口" />
    <add key="DemonsPortTip" value="请确认端口不被其他程序占用" />
    <add key="WsPort" value="工作站服务端口" />
    <add key="WsPortTip" value="请确认端口不被其他程序占用" />
    <add key="OKButton" value="OK" />
    <add key="ErrorMsg" value="输入信息有误，请检查" />
    <add key="MQIPAddress" value="MQ服务器IP地址" />
    <add key="MQPort" value="MQ服务器端口号" />
    <add key="MQUserName" value="MQ服务登录用户名" />
    <add key="MQUserPassword" value="MQ服务登录密码" />
    <add key="MQQueueName" value="MQ队列名" />
    <add key="MQSSL" value="SSL" />
    <add key="AppPath" value="装潢工作站EXE路径" />
    
    <add key="SDSIPAddressVal" value="" />
    <add key="ECSIPAddressVal" value="" />
    <add key="WsPortVal" value="" />
    <add key="WorkPathVal" value="" />
    <add key="DemonsPortVal" value="" />
    <add key="DemonsNameVal" value="" />
    <add key="MQIPAddressVal" value="" />
    <add key="MQPortVal" value="" />
    <add key="MQUserNameVal" value="" />
    <add key="MQUserPasswordVal" value="" />
    <add key="MQQueueNameVal" value="" />
    <add key="MQSSLVal" value="" />
    <add key="AppPathVal" value="" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
  </appSettings>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
</configuration>