﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace PicWorkStationWindowsFormsForALi
{
    public partial class Form1 : Form
    {
        DVNetWrapper.DecorationVRInterface _vrObj;
        VrCallBack _callBack;
        
        private bool _timerComplete = true;
        private bool _initVr = false;
        GraphicsTaskServer _taskServer = null;
        private int callFrameCount = 0;

        public Form1()
        {
            InitializeComponent();
        }

        private void OnServerLoad()
        {
            if (_taskServer.Load())
            {
                btnLoad.Enabled = false;
            }
        }

        void server_Closed(object sender, EventArgs e)
        {
            btnLoad.Enabled = true;
        }

        private void btnLoad_Click(object sender, EventArgs e)
        {
            OnServerLoad();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            NLog.LogManager.GetCurrentClassLogger().Info(Const.KEY_START_APP);

            SetHttpProxy();
            _vrObj = new DVNetWrapper.DecorationVRInterface();

            var dataSvrURL = ConfigurationManager.AppSettings["DataServer"];
            var fileSvrURL = ConfigurationManager.AppSettings["FileServer"];
            var disableCounter = int.Parse(ConfigurationManager.AppSettings["DisableCounter"]);
            var formTimerTick = int.Parse(ConfigurationManager.AppSettings["FormTimerTick"]);
            var titlePrefix = ConfigurationManager.AppSettings["TitlePrefix"];
            //this.Text = titlePrefix + this.Text;

            bool isTestVersion = false;
            if(ConfigurationManager.AppSettings.AllKeys.Contains("IsTestVersion"))
            {
                var testVersion = ConfigurationManager.AppSettings["IsTestVersion"];
                isTestVersion = int.Parse(testVersion) > 0;
            }
            button1.Visible = button2.Visible = textBox1.Visible = isTestVersion;

            int cutBackgroundX = 251;
            if (ConfigurationManager.AppSettings.AllKeys.Contains("CutBackgroundX"))
            {
                var cutX = ConfigurationManager.AppSettings["CutBackgroundX"];
                cutBackgroundX = int.Parse(cutX);
            }

            var dir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory + "res\\");
            _vrObj.SetDownloadServerURL(dataSvrURL, fileSvrURL);
            //_vrObj.SetResousePath(dir);

            try
            {
                var es = new DVNetWrapper.ManagedElevatorSpecification();
                es.ElevatorSizeId = 1;
                es.Width = 160;
                es.Depth = 150;
                es.Height = 240;
                es.DoorHeight = 210;
                es.DoorWidth = 90;
                es.ElevatorCount = 2;

                _vrObj.InitializeElevator(this.vrPanel.Handle, es, DVNetWrapper.ManagedInitType.IT_NEED_CREATE, dir, vrPanel.Width, vrPanel.Height);
                vrPanel_Resize(null, null);

                _callBack = new VrCallBack(cutBackgroundX);
                _callBack.VrInterface = _vrObj;
                _vrObj.SetDecorationVrCallBack(_callBack);

                var arrRGB = _vrObj.GetSceneArrayVisitor("Global_Background_Colors");
                if(arrRGB != null)
                {
                    arrRGB.SetElementValue(0, 0, cutBackgroundX);
                    arrRGB.SetElementValue(0, 1, cutBackgroundX);
                    arrRGB.SetElementValue(0, 2, cutBackgroundX);
                }

                _initVr = true;

                _taskServer = new GraphicsTaskServer();
                _taskServer.Closed += server_Closed;
                _taskServer.VRInterface = _vrObj;
                _taskServer.DisableCounter = disableCounter;
                _taskServer.StationPrefix = titlePrefix.TrimEnd(':', '：') + '_';
                _taskServer.ErrorTextBox = ErrorTextBox;
                _callBack.TaskServer = _taskServer;
                InitArrAutoSnapData();
                timerVrLoop.Interval = formTimerTick;
                timerVrLoop.Enabled = true;
                _vrObj.SetPart((int)DVNetWrapper.ManagedPartTypeId.PartTypeHallConfig, 2, false);
                _vrObj.SetPart((int)DVNetWrapper.ManagedPartTypeId.PartTypeCarConfig, 2, true);


                //var temp = new StringBuilder();
                //var iniFilepath = AppDomain.CurrentDomain.BaseDirectory + "cad3dproe.ini";
                //GetPrivateProfileString("Control", "AutoLoad", "0", temp, 5, iniFilepath);
                //if (temp.ToString() != "0")
                {
                    OnServerLoad();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void SetHttpProxy()
        {
            try
            {
                if (string.IsNullOrEmpty(ConfigurationManager.AppSettings["ProxyUrl"]))
                {
                    return;
                }

                var proxyUrl = ConfigurationManager.AppSettings["ProxyUrl"];
                var proxyUsrName = ConfigurationManager.AppSettings["ProxyName"];
                var proxyPassword = ConfigurationManager.AppSettings["ProxyPsw"];

                WebProxy proxy = new WebProxy(proxyUrl, false);
                proxy.Credentials = new NetworkCredential(proxyUsrName, proxyPassword);
                System.Net.HttpWebRequest.DefaultWebProxy = proxy;
            }
            catch (Exception ex)
            {
                MessageBox.Show(Const.KEY_SET_PROXY_EXCEPTION + ex.Message);
            }
        }

        [System.Runtime.InteropServices.DllImport("kernel32")]
        private static extern long GetPrivateProfileString(string lpApplicationName, string lpKeyName, string lpDefault,
            System.Text.StringBuilder lpReturnedString, int nSize, string lpFileName);

        /// <summary>
        /// 设置自动截图数组中的数据
        /// </summary>
        private void InitArrAutoSnapData()
        {
            int[] datas = new int[]
            {
                Const.CAMERA_POS_VIEW_CAR_FRONT_45,
                Const.CAMERA_POS_VIEW_CAR_BACK_45,
                Const.CAMERA_POS_VIEW_HALL,
            };

            var arr = _vrObj.GetSceneArrayVisitor(Const.AUTO_SNAP_ARRAY_NAME);
            if(arr != null)
            {
                arr.Clear();
                
                for(var i = 0; i < datas.Length; ++i)
                {
                    arr.AddRow();
                    arr.SetElementValue(i, 0, datas[i]);
                }
            }
        }

        private void vrPanel_Resize(object sender, EventArgs e)
        {
            if (_vrObj != null)
            {
                _vrObj.ResizeRender(vrPanel.Width, vrPanel.Height);
                _vrObj.Resize(vrPanel.Width, vrPanel.Height);
                if(_callBack != null)
                {
                    _callBack._vrPanelWidth = vrPanel.Width;
                    _callBack._vrPanelHeight = vrPanel.Height;
                }
            }
        }

        private void timerVrLoop_Tick(object sender, EventArgs e)
        {
            if(!_taskServer.CanRender3D())
            {
                return;
            }

            if (_timerComplete)
            {
                _timerComplete = false;

                if (_initVr)
                {
                    _vrObj.DoVrProcess();

                    if (_taskServer.IsCanPostFrame)
                    {
                        if (callFrameCount >= 1)
                        {
                            _taskServer.CallPostFrame();
                            callFrameCount = 0;
                        }
                        else ++callFrameCount;
                    }
                }

                _timerComplete = true;
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            string content = textBox1.Text;
            if (string.IsNullOrWhiteSpace(content)) return;

            //'WIDTH','160','DEPTH','150','DOORWIDTH','90','DOORHEIGHT','210','CARID','21130','ELEVATORSIZEID','1'
            //,'CARSECURITYWINDOWID','10024','HALLSECURITYWINDOWID','10025'
            //'WIDTH','160','DEPTH','150','DOORWIDTH','90','DOORHEIGHT','210','CARID','10001','ELEVATORSIZEID','1','HALLID','-1','CARTRUNKID','-1','CARTRUNKHEIGHT','100','KEYHOLEID','10014','CARSECURITYWINDOWID','10024','CARSECURITYCAMERAID','10020','CARSECURITYCAMERASETUPPOS','4500','CARSOCKETID','10021','CARCOUPLEID','10022','HALLSECURITYWINDOWID','-1','HALLDISPLAYID','10023','OPENDOOREXTENSION','1','HASCARDOORTRIM','1','CARDOORTRIMMATID','10036','HASHALLDOORTRIM','1','HALLDOORTRIMMATID','10044','HASLEFTPROTECTIONWALL','1','LEFTPROTECTIONWALLMATID','10047','LEFTPROTECTIONWALLHEIGHT','30','HASRIGHTPROTECTIONWALL','1','RIGHTPROTECTIONWALLMATID','10047','RIGHTPROTECTIONWALLHEIGHT','30','HASBACKPROTECTIONWALL','1','BACKPROTECTIONWALLMATID','10047','BACKPROTECTIONWALLHEIGHT','30','SIDEPANELPROTECTIONID','10019','COPID','10052'
            //
            //_callBack.ClearBuildCount();
            //_vrObj.SetPartByString("'WIDTH','160','DEPTH','150','DOORWIDTH','90','DOORHEIGHT','210','CARID','10003','TOPID','-1','TOPMATERIAL01','-1','TOPMATERIAL02','-1','TOPMATERIAL03','-1','HANDRAILID','-1','HANDRAILPOS','-1','COPID','-1','COPMATERIALID','-1','COPBUTTONID','-1','COPDISPLAYID','-1','AUXCOPID','-1','AUXCOPMATERIALID','-1','AUXCOPBUTTONID','-1','AUXCOPDISPLAYID','-1','HDCOPID','-1','HDCOPMATERIALID','-1','HDCOPBUTTONID','-1','HDCOPDISPLAYID','-1','DOORHEADERMATERIALID','-1','FRONTWALLMATERIALID','-1','COPWALLMATERIALID','-1','DOORMATERIALID','-1','LEFTSKIRT','-1','LEFTCOUNT','-1','LEFTELEM1MATERIALID','-1','LEFTELEM2MATERIALID','-1','LEFTELEM3MATERIALID','-1','LEFTELEM4MATERIALID','-1','BACKSKIRT','-1','BACKCOUNT','-1','BACKELEM1MATERIALID','-1','BACKELEM2MATERIALID','-1','BACKELEM3MATERIALID','-1','BACKELEM4MATERIALID','-1','RIGHTSKIRT','-1','RIGHTCOUNT','-1','RIGHTELEM1MATERIALID','-1','RIGHTELEM2MATERIALID','-1','RIGHTELEM3MATERIALID','-1','RIGHTELEM4MATERIALID','-1','BOTTOMMATERIALID','-1','ElevatorSizeId','1','HALLDOORMATERIAL','-1','JAMBID','1001','JAMBMATERIAL','-1','LOPID','10004','LOPMATERIALID','-1','LOPBUTTONID','-1','LOPDISPLAYID','-1','LOPPOS','1','COPPOS','-1','COPPOSX','-1','COPPOSY','-1','AUXCOPPOS','-1','AUXCOPPOSX','-1','AUXCOPPOSY','-1','HDCOPPOS','-1','HDCOPPOSX','75','HDCOPPOSY','-1','FRONTWALLCONSTRUCTIONID','-1','DOOROPENTYPE','-1','HIId','-1','HIPos','-1','HIMaterialId','-1','HIDisplayId','-1','HLId','-1','HLPos','-1','HLMaterialId','-1','HLDisplayId','-1','HallId','2','COPSETUPTYPE','-1','RETAINSKIRTING','-1','MIRRORID','-1','MirrorSetupPos','-1','MirrorSetupPosX','-1','MirrorSetupPosY','-1','HANDRAILLEFTPOSMARK','-1','HANDRAILRIGHTPOSMARK','-1','HANDRAILBACKPOSMARK','-1','SIGHTSEEINGID','-1'");
            //_vrObj.SetPartByString("'DOOROPENTYPE','-1','DOORMATERIALID','-1','COPWALLMATERIALID','-1','FRONTWALLCONSTRUCTIONID','-1','FRONTWALLMATERIALID','-1','MirrorSetupPosX','-1','DOORHEADERMATERIALID','-1','MirrorSetupPos','-1','MirrorSetupPosY','-1','DEPTH','150','WIDTH','160','TOPID','10001','CARID','10030','HANDRAILID','-1','BOTTOMMATERIALID','-1','TOPMATERIAL01','-1','LEFTELEM3MATERIALID','-1','DOORWIDTH','90','LEFTELEM2MATERIALID','-1','LEFTELEM1MATERIALID','-1','SIGHTSEEINGID','-1','TOPMATERIAL02','-1','LEFTCOUNT','-1','HANDRAILBACKPOSMARK','-1','LEFTSKIRT','-1','RETAINSKIRTING','-1','COPSETUPTYPE','-1','HANDRAILRIGHTPOSMARK','-1','HANDRAILLEFTPOSMARK','-1','HDCOPBUTTONID','-1','HDCOPMATERIALID','-1','HDCOPID','-1','AUXCOPPOSY','-1','AUXCOPPOSX','-1','HIMaterialId','-1','AUXCOPPOS','-1','HIPos','-1','AUXCOPDISPLAYID','-1','HIId','-1','HDCOPPOSY','-1','MIRRORID','-1','HDCOPPOSX','75','HDCOPPOS','-1','HallId','2','HDCOPDISPLAYID','-1','HLDisplayId','-1','HLMaterialId','-1','HLPos','-1','HLId','-1','HIDisplayId','-1','COPPOSY','-1','COPPOSX','-1','COPPOS','-1','COPDISPLAYID','-1','COPBUTTONID','-1','HALLDOORMATERIAL','-1','ElevatorSizeId','1','COPMATERIALID','-1','COPID','-1','RIGHTELEM4MATERIALID','-1','HANDRAILPOS','-1','RIGHTELEM3MATERIALID','-1','AUXCOPBUTTONID','-1','LOPDISPLAYID','-1','AUXCOPMATERIALID','-1','LOPBUTTONID','-1','AUXCOPID','-1','LOPMATERIALID','-1','LOPPOS','-1','LOPID','10004','JAMBMATERIAL','-1','JAMBID','1001','BACKELEM2MATERIALID','-1','BACKELEM1MATERIALID','-1','BACKCOUNT','-1','BACKSKIRT','-1','LEFTELEM4MATERIALID','-1','DOORHEIGHT','210','RIGHTELEM2MATERIALID','-1','TOPMATERIAL03','-1','RIGHTELEM1MATERIALID','-1','RIGHTCOUNT','-1','RIGHTSKIRT','-1','BACKELEM4MATERIALID','-1','BACKELEM3MATERIALID','-1'");
            _vrObj.SetPartByString(content);
        }

        static int gIndex = 0;
        private void button2_Click(object sender, EventArgs e)
        {
            var list = new List<int>() { 101, 102, 105, 106, 220,/*110, 2003, 2004, 1010*//*, 1011, 1012*/ };

            if(gIndex >= list.Count) gIndex = 0;

            var arr = _taskServer.VRInterface.GetSceneArrayVisitor("Global_Setting_Parameter");
            arr.SetElementValue(0, 4, list[gIndex]);
            _taskServer.VRInterface.SendVrMessage("msg_fixview", 1);

            ++gIndex;
        }
    }
}
