﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;

namespace PicWorkStationWindowsFormsForALi
{
    class AvRecordTaskProcess : DefaultTaskProcess
    {
        public AvRecordTaskProcess(ITaskServer taskServer)
            :base(taskServer)
        {

        }

        public override bool IsCanProcess(TaskInfo info)
        {
            return info.TaskType == Const.TaskType_AvRecordTask;
        }

        public override void DoProcessPreRunTask(TaskInfo info, object param)
        {
            CreateDirectory(info.WorkPath);
        }
                
        public override bool DoProcessAfterEndTask(TaskInfo taskInfo, object param)
        {
            try
            {
                string srcVideoName = ConcatVideoIfNeed(taskInfo.WorkPath);
                if(string.IsNullOrEmpty(srcVideoName))
                {
                    TaskServer.WriteLog(Const.KEY_VIDEO_NOT_FOUND, Const.MSG_TYPE_ERROR);
                    return false;
                }
                string srcFileName = Path.Combine(taskInfo.WorkPath, srcVideoName);

                var path = Path.Combine(taskInfo.SharedFolder, taskInfo.NewFileDir);
                CreateDirectory(path);
                string destFileName = Path.Combine(path, "output.mp4");

                FileInfo file = new FileInfo(srcFileName); 
                if (file.Exists) 
                { 
                    file.CopyTo(destFileName, true);

                    Directory.Delete(taskInfo.WorkPath, true);
                }
                else
                {
                    TaskServer.WriteLog(string.Format(Const.KEY_FILE_NOT_EXIST_THEN_FAIL_COPY, srcFileName), Const.MSG_TYPE_ERROR);
                    return false;
                }
            }
            catch (Exception ex)
            {
                TaskServer.WriteLog(string.Format(Const.KEY_COPY_FILE_EXCEPTION, ex), Const.MSG_TYPE_ERROR);
                return false;
            }

            return true;
        }

        public override void DoProcessByMultilayer(int multilayer, TaskInfo info, object param)
        {
 
        }

        public static string ExecuteFFmpeg(string path, string args, int timeout)
        {
            using (var process = new Process())
            {
                string errorOutput = "";
                process.StartInfo = new ProcessStartInfo(path);
                process.StartInfo.Arguments = args;
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;
                process.StartInfo.CreateNoWindow = true;
                process.ErrorDataReceived += (sender, e) =>
                {
                    errorOutput += e.Data;
                };
                process.Start();
                process.BeginErrorReadLine();
                process.StandardOutput.ReadToEnd(); 
                process.WaitForExit();

                return errorOutput;
            }
        }

        public string ConcatVideoIfNeed(string path)
        {
            DirectoryInfo info = new DirectoryInfo(path);
            FileInfo[] fileInfos = info.GetFiles("*.mp4");
            Array.Sort(fileInfos, (a, b) => a.CreationTime.CompareTo(b.CreationTime));
            if(fileInfos.Length > 1)
            {
                string[] contents = fileInfos.Select(o => string.Format(@"file '{0}'", o.Name)).ToArray();

                string filesTxt = "files.txt";
                System.IO.File.WriteAllLines(System.IO.Path.Combine(path, filesTxt), contents);

                string ffmpegExe = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ffmpeg.exe");
                string concatName = "ffmpeg.mp4";
                string args = string.Format("-f concat -i {1}/{0} -c copy {1}/{2}", filesTxt, info.Name, concatName);
                ExecuteFFmpeg(ffmpegExe, args, 200);

                return concatName;
            }
            else if(fileInfos.Length == 1)
            {
                return fileInfos[0].Name;
            }

            return string.Empty;
        }
    }
}
