﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <targets>
    <target name="logfile" xsi:type="File" fileName="${shortdate}.txt" 
	layout="${date:format=[yyyy-MM-dd HH\:mm\:ss]} ${message}" 
	archiveEvery="Day"
	maxArchiveFiles="7"/>
  </targets>

  <rules>
    <logger name="*" minlevel="Info" writeTo="logfile" />
  </rules>
</nlog>