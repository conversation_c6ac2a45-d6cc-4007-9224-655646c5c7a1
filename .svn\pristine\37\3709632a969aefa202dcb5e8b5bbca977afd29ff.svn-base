using System;
using System.Collections.Generic;
using System.IO;

namespace PicWorkStationWindowsFormsForALi
{
    public class TaskInfo
    {
        public Guid TaskId { get; set; }
        public string TaskType { get; set; }
        public string TaskName { get; set; }
        public string NewFileDir { get; set; }
        public string UpdateDrwView { get; set; }

        public string WorkPath { get; set; }

        public Guid EpdTaskId { get; set; }

        public string Properties;

        public string MacFilePath { get; set; }

        public string SharedFolder { get; set; }

        public List<object> OutFiles { get; set; }
    }

    public class PartDetailInfo
    {
        public int FixViewID { get; set; }
        public int PartType { get; set; }
        public int TableId { get; set; }
        public int TableRowId { get; set; }
        public int ImgWidth { get; set; }
        public string ImgFileName { get; set; }
        public string ImgSetupFlag { get; set; }
        public string ImgSizeMark { get; set; }
        public string NameSetupFlag { get; set; }
    }
}