﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PicWorkStationWindowsFormsForALi
{
    public interface IImageItemProcess
    {
        string CreateMark { get; set; }
        void DoProcess(int reportId, string param);
    }

    public interface ITaskServer
    {
        DVNetWrapper.DecorationVRInterface VRInterface { get; set; }

        void Log(string text);
        void WriteLog(string msg, int type);
    }
}
