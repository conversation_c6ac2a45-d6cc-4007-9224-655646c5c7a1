﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PicWorkStationWindowsFormsForALi
{
    public interface ITaskProcess
    {
        bool IsCanProcess(TaskInfo info);
        void DoProcessPreRunTask(TaskInfo info, object param);
        bool DoProcessAfterEndTask(TaskInfo info, object param);

        void DoProcessByMultilayer(int multilayer, TaskInfo info, object param);
        bool IsZipCompress(TaskInfo info);
    }

    abstract class TaskProcess : ITaskProcess
    {
        public ITaskServer TaskServer { get; set; }
        public virtual bool IsCanProcess(TaskInfo info)
        {
            return false;
        }
        public virtual void DoProcessPreRunTask(TaskInfo info, object param) { }
        public abstract bool DoProcessAfterEndTask(TaskInfo taskInfo, object param);

        public virtual void DoProcessByMultilayer(int multilayer, TaskInfo info, object param) { }
        public virtual bool IsZipCompress(TaskInfo info) { return true; }
    }
}
