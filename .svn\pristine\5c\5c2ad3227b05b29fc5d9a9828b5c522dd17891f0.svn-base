<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/glasses">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="world" type="Matrix4f" semantic="World" />
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj" />
  <!--<Parameter name="v" type="Matrix4f" semantic="View" />-->
  <Parameter name="tex_01" type="Texture2D" semantic="Texture0" />
  <Parameter name="tex_lm" type="Texture2D" semantic="Texture1" />
  <Parameter name="tex_channel" type="Texture2D" semantic="Texture2" />
  <Parameter name="vec_tex" type="Vector4f" default="1,1,0,0" />
  <Parameter name="vec_channel" type="Vector4f" default="1,1,0,0" />
  <Parameter name="plate_emissive" type="Colorf" default="1,1,1,1" />
  <Parameter name="reflect_strength_diffuse" type="Colorf" default="1,1,1,1" />
  <!--
  x, is main obj, 1:main; -1:aux; 
  y, unused; 
  z, unused;
  -->
  <Parameter name="setting" type="Vector4f" default="1,1,0,0" />
  <Parameter name="is_clip" type="float32" default="0" semantic="EffectGlobal" />
  <Parameter name="clip_plane" type="Vector4f" default="0,1,0,0" semantic="EffectGlobal" />
  <Sampler name="s_tex">
    <Texture value="tex_01" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP" />
    <AddressV value="WRAP" />
    <AddressW value="WRAP" />
  </Sampler>
  <Sampler name="s_lm">
    <Texture value="tex_lm" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="CLAMP" />
    <AddressV value="CLAMP" />
    <AddressW value="CLAMP" />
  </Sampler>
  <Sampler name="s_channel">
    <Texture value="tex_channel" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP" />
    <AddressV value="WRAP" />
    <AddressW value="WRAP" />
  </Sampler>
  <Shader type="glsl">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			uniform mat4 world;
			uniform mat4 vp;
	
			uniform vec4 vec_tex;   
			uniform vec4 vec_channel;
      uniform mediump vec4 setting;
			
			uniform vec4 clip_plane;
			uniform float is_clip;
		
			attribute vec4 a_position; 
			attribute vec2 a_uv0; 
		
			varying vec2 tc_lm;
			varying vec4 tc_tex;
			varying vec2 tc_channel;
		
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
				//LM				
				tc_lm = a_uv0.xy;
				tc_tex.xy  = TRANSFORM_TEXCOORD(a_uv0, vec_tex);	
				tc_channel.xy  = TRANSFORM_TEXCOORD(a_uv0, vec_channel);	

        ComputeClipInVS(tc_tex, a_position, setting, 
        is_clip, clip_plane, world);
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
        <![CDATA[
			precision mediump float;
		
			uniform sampler2D  s_tex;
			uniform sampler2D  s_lm;
			uniform sampler2D  s_channel;
	
			uniform vec4 reflect_strength_diffuse;
			uniform vec4 plate_emissive;
      uniform mediump vec4 setting;
		
			varying vec2 tc_lm;
			varying vec4 tc_tex;
			varying vec2 tc_channel;
		
			void main()
			{ 
        PS_COMPUTE_CLIP;
        
				vec4 col_lm = texture2D(s_lm, tc_lm);
				vec4 col_tex = texture2D(s_tex, tc_tex.xy);
				vec4 col_channel =  texture2D(s_channel, tc_channel);//没用到
				vec4 clr_res = col_lm * col_tex * plate_emissive * 2.0; 
				clr_res.w =  col_channel.w * reflect_strength_diffuse.x;

				gl_FragColor = clr_res;
			}
		]]></GLShader>
  </Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			layout (std140) uniform entity
			{
				mat4 world;
			};
			layout (std140) uniform view
			{
				mat4 vp;
			};
			layout (std140) uniform material
			{
				vec4 vec_tex;
				vec4 vec_channel;
				vec4 reflect_strength_diffuse;
				vec4 plate_emissive;
				mediump vec4 setting;
			};
      
			layout (std140) uniform global
			{
			   vec4 clip_plane;
			   float is_clip;
			};
		
			in vec4 a_position; 
			in vec2 a_uv0; 
		
			out vec2 tc_lm;
			out vec4 tc_tex;
			out vec2 tc_channel;
		
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;
				//LM				
				tc_lm = a_uv0.xy;
				tc_tex.xy  = TRANSFORM_TEXCOORD(a_uv0, vec_tex);	
				tc_channel.xy  = TRANSFORM_TEXCOORD(a_uv0, vec_channel);	

        ComputeClipInVS(tc_tex, a_position, setting, 
        is_clip, clip_plane, world);
			}
			]]></GLShader>
    <GLShader name="PsMain" type="Pixel">
        <![CDATA[
			precision mediump float;
		
			uniform sampler2D  s_tex;
			uniform sampler2D  s_lm;
			uniform sampler2D  s_channel;
			
			layout (std140) uniform material
			{
				vec4 vec_tex;
				vec4 vec_channel;
				vec4 reflect_strength_diffuse;
				vec4 plate_emissive;
				mediump vec4 setting;
			};
		
			in vec2 tc_lm;
			in vec4 tc_tex;
			in vec2 tc_channel;
			out vec4 fragColor;
		
			void main()
			{ 
        PS_COMPUTE_CLIP;
				vec4 col_lm = texture(s_lm, tc_lm);
				vec4 col_tex = texture(s_tex, tc_tex.xy);
				vec4 col_channel =  texture(s_channel, tc_channel);
				vec4 clr_res = col_lm * col_tex * plate_emissive * 2.0; 
				clr_res.w =  col_channel.w * reflect_strength_diffuse.x;

				fragColor = clr_res;
			}
		]]></GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h" /><![CDATA[
    cbuffer cbEntity : register( b0 )
    {    
		matrix world;	  
    };
    cbuffer cbView : register( b2 )
    {    
		matrix vp;
    };
    
    cbuffer cbMaterial : register( b1 )
    {
		float4 vec_tex;   
		float4 vec_channel;
		float4 reflect_strength_diffuse;
		float4 plate_emissive;
		float4 setting;
	  };
    
    cbuffer cbGlobal : register( b3 )
    {
    float4 clip_plane;
    float is_clip;
    };
    
    SamplerState s_tex : register( s0 );
    Texture2D tex_01 : register( t0 );
    
    SamplerState s_lm : register( s1 );
    Texture2D tex_lm : register( t1 );
    
    SamplerState s_channel : register( s2 );
    Texture2D tex_channel : register( t2 );
	
		struct VsInput
		{
			float3     pos:POSITION;
			float2     tc :TEXCOORD; 
		};
    
		struct VsOutput
		{
			float4   pos             :   SV_POSITION ;
			float2   tc_lm           :   TEXCOORD0;
			float4   tc_tex          :   TEXCOORD1;
			float2   tc_channel      :   TEXCOORD2;	
		};
    
		VsOutput VsMain(VsInput input)
		{
			VsOutput o = (VsOutput)0;
		
			float4   r0=mul(float4(input.pos,1),MUL(world, vp));
			o.pos=r0;
			//LM				
			o.tc_lm      = input.tc;
			//Tex		
			o.tc_tex.xy  = TRANSFORM_TEXCOORD(input.tc, vec_tex);
			//Channel	
			o.tc_channel.xy  = TRANSFORM_TEXCOORD(input.tc, vec_channel);

      ComputeClipInVS(o.tc_tex, input.pos, setting, is_clip, clip_plane, world);
      
			return o;
		}
    
		float4 PsMain(VsOutput input) : SV_Target
		{
      PS_COMPUTE_CLIP;
      
			float4 col_lm        =  tex_lm.Sample( s_lm,input.tc_lm) ;
			float4 col_tex       =  tex_01.Sample( s_tex,input.tc_tex.xy) ;
			float4 col_channel   =  tex_channel.Sample( s_channel,input.tc_channel);//没用到
			float4 clr_res        = col_lm *  col_tex * plate_emissive * 2 ; 
			clr_res.w             = col_channel.w *reflect_strength_diffuse.x;

			return clr_res ;
		}
		]]></Shader>
  <Technique name="tech">
    <Pass name="pass" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
</Effect>
