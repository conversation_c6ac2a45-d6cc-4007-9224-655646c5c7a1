﻿using DVNetWrapper;
using Ionic.Zip;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web.Script.Serialization;

namespace PicWorkStationWindowsFormsForALi
{
    enum MsgParaValueType
    {
        MPVT_INT32,
        MPVT_UINT32,
        MPVT_FLOAT32,
        MPVT_DOUBLE,
        MPVT_STRING,
    };

    class RecordParamInfo
    {
        public string ParaName { get; set; }
        public MsgParaValueType ParaType { get; set; }
        public string ParaStrValue { get; set; }
    }

    class VrCallBack : DVNetWrapper.IDecorationVRCallBack
    {
        public int _cameraPos = 0;
        public float _vrPanelWidth = 1.0f;
        public float _vrPanelHeight = 1.0f;

        public bool _findCopImgRatio = false;
        public double _copImgRatioX = 1.0;
        public double _copImgRatioY = 1.0;

        public DVNetWrapper.DecorationVRInterface VrInterface { get; set; }
        public GraphicsTaskServer TaskServer { get; set; }

        public PostEffectOfImageCutBackground _imageCutter;
        public string _curCameraPosImagePath;
        private bool callOnce= true;

        private int taskType = 0;
        private int multilayer = 0;

        public VrCallBack(int clrX)
        {
            _imageCutter = new PostEffectOfImageCutBackground();
            _imageCutter.ColorNumber = clrX;
        }

        public void LoadCompleteCallBack(int wparam, int vali, string vals)
        {
            try
            {
                switch (wparam)
                {
                    case Const.MSG_BUILD_VR_STRING:
                        {
                            TaskServer.SetVRContentString();
                        }
                        break;
                    case Const.MSE_WPARAM_FIX_COP_COMPLETED:
                        {
                            String[] xy = vals.Split(',');
                            _copImgRatioX = Convert.ToDouble(xy[0]);
                            _copImgRatioY = Convert.ToDouble(xy[1]);
                            _findCopImgRatio = true;
                        }
                        break;
                    case Const.MSG_BUILD_COMPLETE:
                        VrInterface.Print(@"D:\textxml-rili1");
                        SetCaptureFlag(-1);
                        if (callOnce)
                        {
                            callOnce = false;
                            GraphicsTaskServer.IsCanWork = true;

                            TaskServer.WriteLog("callOnce == true", Const.MSG_TYPE_ERROR);
                            if (GraphicsTaskServer.IsWorkingCallBack)
                            {
                                TaskServer.UserCallGetCadTask();
                            }

                            //TaskServer.WriteLog(string.Format("TaskServer.IsWorkingCallBack = {0}", TaskServer.IsWorkingCallBack), Const.MSG_TYPE_ERROR);
                        }
                        else
                        {
                            StartTaskProcess();
                        }
                        break;

                    case Const.MSG_CAMERA_POS:

                        if ((GraphicsTaskServer._taskInstance != null) && !IsInvalidCameraPosition())
                        {
                            var taskInstance = GraphicsTaskServer._taskInstance;
                            var info = taskInstance.OutFiles[_cameraPos - 1] as PartDetailInfo;
                            _curCameraPosImagePath = Path.Combine(taskInstance.WorkPath, info.ImgFileName);
                            VrInterface.RenderLargeImage(_curCameraPosImagePath, info.ImgWidth, info.ImgWidth);
                            TaskServer.WriteLog(
                                string.Format(Const.KEY_3D_FIX_VIEW + ": {0}, " + Const.KEY_3D_IMAGE + " : {1}", info.FixViewID, info.ImgFileName),
                                Const.MSG_TYPE_Normal);
                        }
                        break;

                    case Const.MSG_LARGE_PICTURE_COMPLETED:
                        { 
                            int skipX = 2;
                            int skipY = 0;

                            var info = GraphicsTaskServer._taskInstance.OutFiles[_cameraPos-1] as PartDetailInfo;
                            if (info!=null && _findCopImgRatio)
                            {//如果是一体式或者嵌入式COP
                                skipX = (int)(info.ImgWidth * (1 - _copImgRatioX) * 0.5);
                                skipY = (int)(info.ImgWidth * (1 - _copImgRatioY) * 0.5);
                            }
                            _imageCutter.SkipWidth = skipX;
                            _imageCutter.SkipHeight = skipY;
                            _imageCutter.DoProcess(taskType, _curCameraPosImagePath);
                            ++_cameraPos;
                            if (IsInvalidCameraPosition())
                            {
                                LargePicCompleted();
                            }
                            else
                            {
                                var item = NextItem();
                                if(item != null)
                                {
                                    VrInterface.SetSnapPos(_cameraPos);
                                    VrInterface.Resize(item.ImgWidth, item.ImgWidth);
                                    VrInterface.SendVrMessage("msg_snappicture", 2);
                                }
                                else
                                {
                                    LargePicCompleted();
                                }
                            }
                            _findCopImgRatio = false;
                        }
                        
                        break;
                    case Const.MSG_BUILD_ERROR:
                        {
                            TaskServer.VrBuildError();
                        }
                        break;
                    case Const.MSG_GEN_PANORAMA_COMPLETE:
                        {
                            MatchPanoramaImageCompleted(vals);
                        }
                        break;
                    case Const.MSG_GEN_RECORD_PATH_COMPLETE:
                        {
                            TaskServer.EventHandlerPostFrame += new EventHandler<EventArgs>((o, e1) =>
                            {
                                RecordVideoCompleted();
                            });
                        }
                        break;
                    default: break;
                }
            }
            catch (Exception ex)
            {
                TaskServer.WriteLog(Const.KEY_3D_CB_EXCEPTION + ex.Message, Const.MSG_TYPE_ERROR);
                TaskServer.VrBuildError();
            }
        }

        private void StartTaskProcess()
        {
            TaskServer.WriteLog("StartTaskProcess() start...", Const.MSG_TYPE_ERROR);
            multilayer = TaskServer.VRContentIndex;
            if (multilayer < 0) multilayer = 0;
            //TaskServer.BuildTestData();
            //GraphicsTaskServer._taskInstance.TaskType = Const.TaskType_DefaultTask;
            //new PartDetailReportTaskProcess(TaskServer).DoProcessPreRunTask(GraphicsTaskServer._taskInstance, null);
            //VrInterface.Print(@"D:\textxml");

            StartProcessAfterVrFinished();
            //TaskServer.WriteLog("3D build completed!", Const.MSG_TYPE_Normal);
        }

        private string GetRecordArrayName()
        {
            int a = 1;
            int b = multilayer < 0 ? 0 : multilayer;
            var setting_car = TaskServer.VRInterface.GetSceneArrayVisitor("Global_Setting_Car");
            if (setting_car != null)
            {
                int x = (int)setting_car.GetElementFloat(0, 3);
                int z = (int)setting_car.GetElementFloat(0, 5);
                if(x == 160 && z == 150)
                {
                    a = 1;
                }
                else if(x == 200 && z == 155)
                {
                    a = 2;
                }
                else if(x == 110 && z == 210)
                {
                    a = 3;
                }
                else if(x == 105 && z == 100)
                {
                    a = 4;
                }
            }

            return string.Format("Record_Path_{0:D2}_{1}", a, b);
        }

        private void StartProcessAfterVrFinished()
        {
            taskType = 0;
            if (TaskServer == null || GraphicsTaskServer._taskInstance == null)
                return;
            TaskServer.WriteLog("StartProcessAfterVrFinished() start...", Const.MSG_TYPE_Normal);

            //GraphicsTaskServer.IsCanWork = false;
            var task = GraphicsTaskServer._taskInstance;
            //task.TaskType = Const.TaskType_DefaultTask;
            if(!int.TryParse(task.TaskType, out taskType))
            {
                taskType = 1;
            }

            switch (task.TaskType)
            {
                case Const.TaskType_AvRecordTask:
                    {
                        ////测试代码
                        //var tp = TaskServer.CurrentTaskProcess;
                        //if (tp != null)
                        //{
                        //    tp.DoProcessPreRunTask(task, null);
                        //}
                        ///////////////////

                        //根据multilayer的取值，重置vr message和mp4文件名
                        string recordArrayName = GetRecordArrayName();
                        ManagedMsgParaValue[] paras = { new ManagedMsgParaValue("record_array_name", ManagedMsgParaValueType.MPVT_STRING, recordArrayName) };
                        VrInterface.SendVrMessage("msg_recordpath1", paras, 1);
                        string avPath = Path.Combine(task.WorkPath, Const.VideoFileNames[multilayer]);

                        var sb = new StringBuilder();
                        var serializer = new JavaScriptSerializer();
                        serializer.Serialize(new List<object>() { new RecordParamInfo() { ParaName="avfile", ParaType=MsgParaValueType.MPVT_STRING, ParaStrValue=avPath,}}, sb);
                        VrInterface.SendVrMessageWithParas("msg_noclip_screen_record", sb.ToString(), 2);
                    }
                    break;
                case Const.TaskType_PanoramaTask:
                    {
                        var dir = task.WorkPath;
                        if (Path.DirectorySeparatorChar != dir[dir.Length - 1]) dir += Path.DirectorySeparatorChar;
                        VrInterface.RenderPanoramaImages(dir);
                    }
                    break;
                case Const.TaskType_PartDetailReportTask:
                case Const.TaskType_WordReportTask:
                case Const.TaskType_DefaultTask:
                    {
                        SetCaptureFlag(1);
                        //根据multilayer的取值，重置arr_AutoSnap_Info和TaskInfo.OutFiles
                        var tp = TaskServer.CurrentTaskProcess;
                        if (tp != null)
                        {
                            tp.DoProcessByMultilayer(multilayer, task, null);
                        }
                        _cameraPos = 1;
                        if (!IsInvalidCameraPosition())
                        {
                            var info = NextItem();
                            VrInterface.SetSnapPos(_cameraPos);
                            VrInterface.Resize(info.ImgWidth, info.ImgWidth);

                            {
                                ManagedMsgParaValue[] paras = 
                                {
                                    new ManagedMsgParaValue("FloorMark", ManagedMsgParaValueType.MPVT_INT32, 2),
                                    new ManagedMsgParaValue("RunState", ManagedMsgParaValueType.MPVT_INT32, 1),
                                    new ManagedMsgParaValue("Reset", ManagedMsgParaValueType.MPVT_INT32, 0),
                                    new ManagedMsgParaValue("Light", ManagedMsgParaValueType.MPVT_INT32, 1),
                                };
                                VrInterface.SendVrMessage("msg_change_car_states", paras, 1);
                            }

                            VrInterface.SendVrMessage("msg_snappicture", 2);
                        }
                    }
                    break;
                default:
                    GraphicsTaskServer.IsCanWork = true;
                    TaskServer.WriteLog(Const.KEY_TASK_TYPE_NOT_SUPPORT + Helper.ToString(GraphicsTaskServer._taskInstance), Const.MSG_TYPE_Normal);
                    break;
            }
        }
        private PartDetailInfo NextItem()
        {
            var outFiles = GraphicsTaskServer._taskInstance.OutFiles;
            while (_cameraPos - 1 < outFiles.Count)
            {
                var info = outFiles[_cameraPos - 1] as PartDetailInfo;
                long partId = TaskServer.VRInterface.GetCurrentPartId(info.PartType);
                //partId <= 10是排除掉那些默认系统的部件(一般都是2)
                if (info.PartType != 200 && info.PartType != 200000 && partId <= 10)
                {
                    ++_cameraPos;
                }
                else
                {
                    return info;
                }
            }

            return null;
        }

        private void SetCaptureFlag(int val)
        {
            var g_select = TaskServer.VRInterface.GetSceneArrayVisitor("Global_Select");
            if (g_select != null)
            {
                var col = g_select.GetColumnCount() - 1;
                g_select.SetElementValue(0, col, val);
            }
        }

        private bool IsInvalidCameraPosition()
        {
            var outFiles = GraphicsTaskServer._taskInstance.OutFiles;
            if (outFiles == null) return true;

            return _cameraPos > outFiles.Count || _cameraPos <= 0;
        }

        private void LargePicCompleted()
        {
            SetCaptureFlag(-1);
            _cameraPos = 0;
            VrInterface.SetSnapPos(_cameraPos);
            VrInterface.Resize(_vrPanelWidth, _vrPanelHeight);
            VrInterface.SendVrMessage("msg_clear_fixview_camera_state", 0);
            VrInterface.SendVrMessage("msg_gotohall", 1);
            VrInterface.SendVrMessage("msg_enter360", 2);
			VrInterface.SendVrMessage("msg_change_car_states", 3);
            //TaskServer.RecoverTimer();

            if (TaskServer.IsEndVRContentString())
            {
                if (GraphicsTaskServer._taskInstance != null)
                {
                    TaskServer.OnEndGeneration(true);
                }
            }
            else
            {
                //generate next
                ManagedMsgParaValue[] paras = 
                {
                    new ManagedMsgParaValue("wParam", ManagedMsgParaValueType.MPVT_INT32, Const.MSG_BUILD_VR_STRING), 
                };
                VrInterface.SendVrMessage("msg_callback", paras, 3);
            }
        }

        private void MatchPanoramaImageCompleted(string dir)
        {
            if (TaskServer != null && GraphicsTaskServer._taskInstance != null)
            {
                GraphicsTaskServer._taskInstance.WorkPath = dir;
                TaskServer.OnEndGeneration(true);
            }
        }
        private void RecordVideoCompleted()
        {
            VrInterface.SendVrMessage("msg_enter360", 2);
            if (TaskServer.IsEndVRContentString())
            {
                if (GraphicsTaskServer._taskInstance != null)
                {
                    TaskServer.OnEndGeneration(true);
                    ////测试代码
                    //var tp = TaskServer.CurrentTaskProcess;
                    //if (tp != null)
                    //{
                    //    tp.DoProcessAfterEndTask(GraphicsTaskServer._taskInstance, null);
                    //}
                    ////////////////////////////////////
                }
            }
            else
            {
                //generate next
                ManagedMsgParaValue[] paras = 
                {
                    new ManagedMsgParaValue("wParam", ManagedMsgParaValueType.MPVT_INT32, Const.MSG_BUILD_VR_STRING), 
                };
                VrInterface.SendVrMessage("msg_callback", paras, 3);
            }
        }

        public string DownloadData(string download_url, string post_data)
        {
            //throw new NotImplementedException();
            return NetLinkDownLoad(download_url, post_data);
        }

        public bool DownloadFile(string download_url, string file_abs_path)
        {
            if(Download(download_url, file_abs_path))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        //从服务端下载数据
        public string NetLinkDownLoad(string url, string serializeData)
        {
            string responseContent = "";
            try
            {
                System.GC.Collect(); 
                var request = WebRequest.Create(url) as HttpWebRequest;
                request.Method = "Post";
                byte[] btBodys = Encoding.UTF8.GetBytes(serializeData);
                request.ContentLength = btBodys.Length;
                request.ContentType = "application/json";
                request.GetRequestStream().Write(btBodys, 0, btBodys.Length);

                HttpWebResponse httpWebResponse = request.GetResponse() as HttpWebResponse;
                using (StreamReader streamReader = new StreamReader(httpWebResponse.GetResponseStream()))
                {
                    responseContent = streamReader.ReadToEnd();

                    streamReader.Close();
                }
                request.Abort();
                httpWebResponse.Close();
            }
            catch (System.Exception ex)
            {
                TaskServer.WriteLog(string.Format("{0}:{1}:{2}", url, Const.KEY_DOWNLOAD_FILE_EXCEPTION, ex.Message), Const.MSG_TYPE_ERROR);
            }
            return responseContent;
        }

        //Http方式下载文件
        public bool Download(string url, string localfile)
        {
            bool flag = false;
            long startPosition = 0; // 上次下载的文件起始位置
            FileStream writeStream = null; // 写入本地文件流对象
            try
            {
                string creatFilePath = localfile.Replace("/", @"\");
                int pos = creatFilePath.LastIndexOf(@"\");
                creatFilePath = creatFilePath.Substring(0, pos);
                Directory.CreateDirectory(creatFilePath);

                long remoteFileLength = GetHttpLength(url);// 取得远程文件长度
                if (remoteFileLength == 745)
                {
                    TaskServer.WriteLog(string.Format("{0}:{1}", url, Const.KEY_DOWNLOAD_FILE_EXCEPTION), Const.MSG_TYPE_ERROR);
                    TaskServer.VrBuildError(); 
                    return false;
                }

                // 判断要下载的文件夹是否存在
                if (File.Exists(localfile))
                {

                    writeStream = File.OpenWrite(localfile);             // 存在则打开要下载的文件
                    startPosition = writeStream.Length;                  // 获取已经下载的长度

                    if (startPosition >= remoteFileLength)
                    {
                        writeStream.Close();

                        TaskServer.WriteLog(string.Format("{0}:{1}", url, Const.KEY_DOWNLOAD_FILE_EXCEPTION), Const.MSG_TYPE_ERROR);
                        TaskServer.VrBuildError();
                        return false;
                    }
                    else
                    {
                        writeStream.Seek(startPosition, SeekOrigin.Current); // 本地文件写入位置定位
                    }
                }
                else
                {
                    writeStream = new FileStream(localfile, FileMode.Create);// 文件不保存创建一个文件
                    startPosition = 0;
                }



                HttpWebRequest myRequest = (HttpWebRequest)HttpWebRequest.Create(url);// 打开网络连接

                if (startPosition > 0)
                {
                    myRequest.AddRange((int)startPosition);// 设置Range值,与上面的writeStream.Seek用意相同,是为了定义远程文件读取位置
                }


                Stream readStream = myRequest.GetResponse().GetResponseStream();// 向服务器请求,获得服务器的回应数据流


                byte[] btArray = new byte[512];// 定义一个字节数据,用来向readStream读取内容和向writeStream写入内容
                int contentSize = readStream.Read(btArray, 0, btArray.Length);// 向远程文件读第一次

                long currPostion = startPosition;

                while (contentSize > 0)// 如果读取长度大于零则继续读
                {
                    currPostion += contentSize;
                    int percent = (int)(currPostion * 100 / remoteFileLength);
                    System.Console.WriteLine("percent=" + percent + "%");

                    writeStream.Write(btArray, 0, contentSize);// 写入本地文件
                    contentSize = readStream.Read(btArray, 0, btArray.Length);// 继续向远程文件读取
                }

                //关闭流
                writeStream.Close();
                readStream.Close();

                flag = true;        //返回true下载成功

            }
            catch (Exception ex)
            {
                TaskServer.WriteLog(string.Format("{0}:{1}:{2}", url, Const.KEY_DOWNLOAD_FILE_EXCEPTION, ex.Message), Const.MSG_TYPE_ERROR);
                writeStream.Close();
                flag = false;       //返回false下载失败
            }

            return flag;
        }

        // 从文件头得到远程文件的长度
        private long GetHttpLength(string url)
        {
            long length = 0;

            try
            {
                HttpWebRequest req = (HttpWebRequest)HttpWebRequest.Create(url);// 打开网络连接
                HttpWebResponse rsp = (HttpWebResponse)req.GetResponse();

                if (rsp.StatusCode == HttpStatusCode.OK)
                {
                    length = rsp.ContentLength;// 从文件头得到远程文件的长度
                }

                rsp.Close();
                return length;
            }
            catch (Exception ex)
            {
                TaskServer.WriteLog(string.Format("{0}:{1}:{2}", url, Const.KEY_GET_FILE_LEN, ex.Message), Const.MSG_TYPE_ERROR);
                return length;
            }

        }

        public void OnProgress(int category, int progress, string info)
        {
            //if (category == 0 && TaskServer != null)
            //{
            //    TaskServer.WriteLog(info, Const.MSG_TYPE_Normal);
            //}
        }

        public bool GetReceiveProgress()
        {
            return true;
        }

        public void SetReceiveProgress(bool val)
        {
            
        }
    }
}
