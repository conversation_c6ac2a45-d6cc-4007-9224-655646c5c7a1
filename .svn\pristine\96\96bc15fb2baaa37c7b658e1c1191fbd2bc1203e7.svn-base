<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/combine2texturescube">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="emissive" type="Colorf" default="1,1,1,1" />
  <Parameter name="tex01" type="Texture2D" semantic="Texture0" />
  <Parameter name="tex02" type="TextureCube" semantic="Texture1" />
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj" />
  <Parameter name="iwt" type="Matrix4f" semantic="InverseWorldTranspose" />
  <Parameter name="ivt" type="Matrix4f" semantic="InverseViewTranspose" />
  <Parameter name="iw" type="Matrix4f" semantic="InverseWorld" />
  <Parameter name="v" type="Matrix4f" semantic="View" />
  <Parameter name="cwp" type="Vector3f" semantic="CameraWorldPosition" />
  <Parameter name="world" type="Matrix4f" semantic="World" />
  <Parameter name="tex_gen" type="float32" default="0" />
  <Parameter name="is_clip" type="float32" default="0" semantic="EffectGlobal" />
  <Parameter name="clip_plane" type="Vector4f" default="0,1,0,0" semantic="EffectGlobal" />
  <!--必须有Texture；其余可选-->
  <Sampler name="sampler01">
    <Texture value="tex01" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP" />
    <AddressV value="WRAP" />
    <AddressW value="WRAP" />
  </Sampler>
  <Sampler name="sampler02">
    <Texture value="tex02" />
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR" />
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="MIRROR" />
    <AddressV value="MIRROR" />
    <AddressW value="MIRROR" />
  </Sampler>
  <!--必须有type:hlsl;cg;glsl;目前实现了hlsl-->
  <Shader type="glsl">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			uniform mat4 vp;
			uniform mat4 iwt;
			uniform mat4 ivt;
			uniform mat4 v;
			uniform mat4 iw;
			
			uniform mat4 world;    
			uniform vec4 clip_plane;
			uniform mediump float is_clip;
			uniform float tex_gen;
			uniform vec3 cwp;
			
			attribute vec4 a_position; 
			attribute vec4 a_normal; 
			attribute vec2 a_uv0; 
			
			varying vec4 uv_0;
			varying vec4 uv_1;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				uv_0.xy = a_uv0.xy;
				uv_1.xyz = normalize((a_position * MUL(world, v)).xyz);
	  
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					uv_1.w = dot(world_pos, clip_plane);
				}

				if(tex_gen == 1.0) //reflect
				{
					uv_1.xy = reflect(uv_1.xyz, a_normal.xyz).xy;
				}
        else if(tex_gen == 2.0) //skymap
        {
				  uv_1.xyz = (a_position * world).xyz-cwp.xyz;
        }
			}
			]]></GLShader>
    <GLShader name="PsModulate" type="Pixel"><![CDATA[
            precision mediump float;
            uniform vec4 emissive;
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			uniform vec4 clip_plane;
			uniform mediump float is_clip;
			
			varying vec4 uv_0;
			varying vec4 uv_1;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture2D(sampler01, uv_0.xy);
				vec4 clr_arg2 = textureCube(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 * clr_arg2;

				gl_FragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);			
			}
			]]></GLShader>
    <GLShader name="PsModulate2X" type="Pixel"><![CDATA[
                precision mediump float;
			uniform vec4 emissive;
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			uniform vec4 clip_plane;
			uniform mediump float is_clip;
			
			varying vec4 uv_0;
			varying vec4 uv_1;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture2D(sampler01, uv_0.xy);
				vec4 clr_arg2 = textureCube(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 * clr_arg2 * 2.0;
	  
				gl_FragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);			
			}
			]]></GLShader>
    <GLShader name="PsModulate4X" type="Pixel"><![CDATA[
                precision mediump float;
			uniform vec4 emissive;
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			uniform vec4 clip_plane;
			uniform mediump float is_clip;
			
			varying vec4 uv_0;
			varying vec4 uv_1;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture2D(sampler01, uv_0.xy);
				vec4 clr_arg2 = textureCube(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 * clr_arg2 * 4.0;
	  
				gl_FragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);			
			}
			]]></GLShader>
    <GLShader name="PsAdd" type="Pixel"><![CDATA[
                precision mediump float;
			uniform vec4 emissive;
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			uniform vec4 clip_plane;
			uniform mediump float is_clip;
			
			varying vec4 uv_0;
			varying vec4 uv_1;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture2D(sampler01, uv_0.xy);
				vec4 clr_arg2 = textureCube(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 + clr_arg2;
	  
				gl_FragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);		
			}
			]]></GLShader>
    <GLShader name="PsAddSigned" type="Pixel"><![CDATA[
                precision mediump float;
			uniform vec4 emissive;
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			uniform vec4 clip_plane;
			uniform mediump float is_clip;
			
			varying vec4 uv_0;
			varying vec4 uv_1;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture2D(sampler01, uv_0.xy);
				vec4 clr_arg2 = textureCube(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 + clr_arg2 - 0.5;
	  
				gl_FragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);	
			}
			]]></GLShader>
    <GLShader name="PsAddSigned2X" type="Pixel"><![CDATA[
                precision mediump float;
			uniform vec4 emissive;
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			uniform vec4 clip_plane;
			uniform mediump float is_clip;
			
			varying vec4 uv_0;
			varying vec4 uv_1;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture2D(sampler01, uv_0.xy);
				vec4 clr_arg2 = textureCube(sampler02, uv_1.xyz);
				vec4 clr = (clr_arg1 + clr_arg2 - 0.5) * 2.0;
	  
				gl_FragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);	
			}
			]]></GLShader>
    <GLShader name="PsSubtract" type="Pixel"><![CDATA[
                precision mediump float;
			uniform vec4 emissive;
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			uniform vec4 clip_plane;
			uniform mediump float is_clip;
			
			varying vec4 uv_0;
			varying vec4 uv_1;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture2D(sampler01, uv_0.xy);
				vec4 clr_arg2 = textureCube(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 - clr_arg2;
	  
				gl_FragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);
			}
			]]></GLShader>
    <GLShader name="PsAddSmooth" type="Pixel"><![CDATA[
                precision mediump float;
			uniform vec4 emissive;
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			uniform vec4 clip_plane;
			uniform mediump float is_clip;
			
			varying vec4 uv_0;
			varying vec4 uv_1;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture2D(sampler01, uv_0.xy);
				vec4 clr_arg2 = textureCube(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 + clr_arg2 - clr_arg1 * clr_arg2;
	  
				gl_FragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);
			}
			]]></GLShader>
  </Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h" />
    <GLShader name="VsMain" type="Vertex">
        <![CDATA[
			layout (std140) uniform entity
			{
				mat4 world;
				mat4 iw;
			 	mat4 iwt;
			};
			layout (std140) uniform view
			{
				mat4 v;
				mat4 vp;
				vec3 cwp;
				mat4 ivt;
			};
			layout (std140) uniform global
			{
			   vec4 clip_plane;
			   mediump float is_clip;
			};
			layout (std140) uniform material
			{
				float tex_gen;
				vec4 emissive;
			};
	
			in vec4 a_position; 
			in vec4 a_normal; 
			in vec2 a_uv0; 
			
			out vec4 uv_0;
			out vec4 uv_1;
			
			void main() 
			{
				gl_Position = a_position * MUL(world, vp);
				uv_0.xy = a_uv0.xy;
				uv_1.xyz = normalize((a_position * MUL(world, v)).xyz);
	  
				if(is_clip > 0.0)
				{
					vec4 world_pos = a_position * world;
					uv_1.w = dot(world_pos, clip_plane);
				}

				if(tex_gen == 1.0) //reflect
				{
					uv_1.xy = reflect(uv_1.xyz, a_normal.xyz).xy;
				}
        else if(tex_gen == 2.0) //skymap
        {
				  uv_1.xyz = (a_position * world).xyz-cwp.xyz;
        }
			}
			]]></GLShader>
    <GLShader name="PsModulate" type="Pixel"><![CDATA[
            precision mediump float;
			layout (std140) uniform material
			{
				float tex_gen;
				vec4 emissive;
			};
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			layout (std140) uniform global
			{
			   vec4 clip_plane;
			   mediump float is_clip;
			};
			
			in vec4 uv_0;
			in vec4 uv_1;
			out vec4 fragColor;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture(sampler01, uv_0.xy);
				vec4 clr_arg2 = texture(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 * clr_arg2;

				fragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);			
			}
			]]></GLShader>
    <GLShader name="PsModulate2X" type="Pixel"><![CDATA[
                precision mediump float;
			layout (std140) uniform material
			{
				float tex_gen;
				vec4 emissive;
			};
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			layout (std140) uniform global
			{
			   vec4 clip_plane;
			   mediump float is_clip;
			};
			
			in vec4 uv_0;
			in vec4 uv_1;
			out vec4 fragColor;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture(sampler01, uv_0.xy);
				vec4 clr_arg2 = texture(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 * clr_arg2 * 2.0;
	  
				fragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);			
			}
			]]></GLShader>
    <GLShader name="PsModulate4X" type="Pixel"><![CDATA[
                precision mediump float;
			layout (std140) uniform material
			{
				float tex_gen;
				vec4 emissive;
			};
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			layout (std140) uniform global
			{
			   vec4 clip_plane;
			   mediump float is_clip;
			};
			
			in vec4 uv_0;
			in vec4 uv_1;
			out vec4 fragColor;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture(sampler01, uv_0.xy);
				vec4 clr_arg2 = texture(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 * clr_arg2 * 4.0;
	  
				fragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);			
			}
			]]></GLShader>
    <GLShader name="PsAdd" type="Pixel"><![CDATA[
                precision mediump float;
			layout (std140) uniform material
			{
				float tex_gen;
				vec4 emissive;
			};
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			layout (std140) uniform global
			{
			   vec4 clip_plane;
			   mediump float is_clip;
			};
			
			in vec4 uv_0;
			in vec4 uv_1;
			out vec4 fragColor;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture(sampler01, uv_0.xy);
				vec4 clr_arg2 = texture(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 + clr_arg2;
	  
				fragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);		
			}
			]]></GLShader>
    <GLShader name="PsAddSigned" type="Pixel"><![CDATA[
                precision mediump float;
			layout (std140) uniform material
			{
				float tex_gen;
				vec4 emissive;
			};
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			layout (std140) uniform global
			{
			   vec4 clip_plane;
			   mediump float is_clip;
			};
			
			in vec4 uv_0;
			in vec4 uv_1;
			out vec4 fragColor;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture(sampler01, uv_0.xy);
				vec4 clr_arg2 = texture(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 + clr_arg2 - 0.5;
	  
				fragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);	
			}
			]]></GLShader>
    <GLShader name="PsAddSigned2X" type="Pixel"><![CDATA[
                precision mediump float;
			layout (std140) uniform material
			{
				float tex_gen;
				vec4 emissive;
			};
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			layout (std140) uniform global
			{
			   vec4 clip_plane;
			   mediump float is_clip;
			};
			
			in vec4 uv_0;
			in vec4 uv_1;
			out vec4 fragColor;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture(sampler01, uv_0.xy);
				vec4 clr_arg2 = texture(sampler02, uv_1.xyz);
				vec4 clr = (clr_arg1 + clr_arg2 - 0.5) * 2.0;
	  
				fragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);	
			}
			]]></GLShader>
    <GLShader name="PsSubtract" type="Pixel"><![CDATA[
                precision mediump float;
			layout (std140) uniform material
			{
				float tex_gen;
				vec4 emissive;
			};
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			layout (std140) uniform global
			{
			   vec4 clip_plane;
			   mediump float is_clip;
			};
			
			in vec4 uv_0;
			in vec4 uv_1;
			out vec4 fragColor;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture(sampler01, uv_0.xy);
				vec4 clr_arg2 = texture(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 - clr_arg2;
	  
				fragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);
			}
			]]></GLShader>
    <GLShader name="PsAddSmooth" type="Pixel"><![CDATA[
                precision mediump float;
			layout (std140) uniform material
			{
				float tex_gen;
				vec4 emissive;
			};
			uniform sampler2D sampler01;
			uniform samplerCube sampler02;
			
			layout (std140) uniform global
			{
			   vec4 clip_plane;
			   mediump float is_clip;
			};
			
			in vec4 uv_0;
			in vec4 uv_1;
			out vec4 fragColor;
			
			void main() 
			{
				if(is_clip > 0.0 && uv_1.w < 0.0)  { discard; }
      
				vec4 clr_arg1 = texture(sampler01, uv_0.xy);
				vec4 clr_arg2 = texture(sampler02, uv_1.xyz);
				vec4 clr = clr_arg1 + clr_arg2 - clr_arg1 * clr_arg2;
	  
				fragColor = vec4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);
			}
			]]></GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h" />
    <!--一个Shader可以有零个或多个Include，必须有file：指定文件名--><![CDATA[
    cbuffer cbEntity : register( b0 )
    {
    float4x4 world; 
	  float4x4 iw;
	  float4x4 iwt;    
    };
    cbuffer cbView : register( b3 )
    {
	  float4x4 v;
    float4x4 vp;
    float3 cwp;    
	  float4x4 ivt;
    };
    
    cbuffer cbMaterial : register( b1 )
    {
    float4 emissive;
    float tex_gen;
    };
    
    cbuffer cbGlobal : register( b2 )
    {
    float4 clip_plane;
    float is_clip;
    };

    SamplerState sampler01 : register( s0 );
    Texture2D tex01 : register( t0 );
    
		SamplerState sampler02 : register( s1 );
    TextureCube tex02 : register( t1 );

    struct VsInput
    {
      float3 pos : POSITION;
      float3 normal : NORMAL;
	  float4 uv : TEXCOORD0;
    };
    
    struct VsOutput
    {
      float4 pos : SV_POSITION;
      float4 uv_0 : TEXCOORD0;
	  float4 uv_1 : TEXCOORD1;
	  float3 normal : TEXCOORD2;
    };
    
    VsOutput VsMain(VsInput input)
    {
      VsOutput o = (VsOutput)0;
      o.pos = mul(float4(input.pos, 1), MUL(world, vp));
      o.uv_0.xy = input.uv.xy;
      o.normal = normalize(mul(float4(input.normal, 0), MUL(iwt, ivt)).xyz);
      o.uv_1.xyz = normalize(mul(float4(input.pos,1), MUL(world, v)).xyz);
	  
      if(is_clip > 0)
      {
        float4 world_pos = mul(float4(input.pos, 1), world);
        o.uv_1.w = dot(world_pos, clip_plane);
      }

	  if(tex_gen == 1) //reflect
	  {
	     o.uv_1.xy = reflect(o.uv_1.xyz, input.normal).xy;
	  }
    else if(tex_gen == 2) //skybox
    {
      o.uv_1.xyz = mul(float4(input.pos, 1), world).xyz-cwp.xyz;
    }
	  
      return o;
    }
    
    float4 PsModulate(VsOutput input) : SV_TARGET
    { 	 
      if(is_clip > 0)  { clip(input.uv_1.w); }
      
      float4 clr_arg1 = tex01.Sample(sampler01, input.uv_0.xy);
	    float4 clr_arg2 = tex02.Sample(sampler02, input.uv_1.xyz);
	    float4 clr = clr_arg1 * clr_arg2;

	    return float4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);
    }
	
	float4 PsModulate2X(VsOutput input) : SV_TARGET
    {
	  if(is_clip > 0)  { clip(input.uv_1.w); }
      
	  float4 clr_arg1 = tex01.Sample(sampler01, input.uv_0.xy);
	  float4 clr_arg2 = tex02.Sample(sampler02, input.uv_1.xyz);
	  float4 clr = clr_arg1 * clr_arg2 * 2;
	  
	  return float4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);
	}
	
	float4 PsModulate4X(VsOutput input) : SV_TARGET
    {
	  if(is_clip > 0)  { clip(input.uv_1.w); }
      
	  float4 clr_arg1 = tex01.Sample(sampler01, input.uv_0.xy);
	  float4 clr_arg2 = tex02.Sample(sampler02, input.uv_1.xyz);
	  float4 clr = clr_arg1 * clr_arg2 * 4;
	  
	  return float4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);
	}
	
	float4 PsAdd(VsOutput input) : SV_TARGET
    {
	  if(is_clip > 0)  { clip(input.uv_1.w); }
      
	  float4 clr_arg1 = tex01.Sample(sampler01, input.uv_0.xy);
	  float4 clr_arg2 = tex02.Sample(sampler02, input.uv_1.xyz);
	  float4 clr = clr_arg1 + clr_arg2;
	  
	  return float4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);
	}
	
	float4 PsAddSigned(VsOutput input) : SV_TARGET
    {
	  if(is_clip > 0)  { clip(input.uv_1.w); }
      
	  float4 clr_arg1 = tex01.Sample(sampler01, input.uv_0.xy);
	  float4 clr_arg2 = tex02.Sample(sampler02, input.uv_1.xyz);
	  float4 clr = clr_arg1 + clr_arg2 - 0.5;
	  
	  return float4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);
	}
	
	float4 PsAddSigned2X(VsOutput input) : SV_TARGET
    {
	  if(is_clip > 0)  { clip(input.uv_1.w); }
      
	  float4 clr_arg1 = tex01.Sample(sampler01, input.uv_0.xy);
	  float4 clr_arg2 = tex02.Sample(sampler02, input.uv_1.xyz);
	  float4 clr = (clr_arg1 + clr_arg2 - 0.5) * 2;
	  
	  return float4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);
	}
	
	float4 PsSubtract(VsOutput input) : SV_TARGET
    {
	  if(is_clip > 0)  { clip(input.uv_1.w); }
      
	  float4 clr_arg1 = tex01.Sample(sampler01, input.uv_0.xy);
	  float4 clr_arg2 = tex02.Sample(sampler02, input.uv_1.xyz);
	  float4 clr = clr_arg1 - clr_arg2;
	  
	  return float4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);
	}
	
	float4 PsAddSmooth(VsOutput input) : SV_TARGET
    {
	  if(is_clip > 0)  { clip(input.uv_1.w); }
      
	  float4 clr_arg1 = tex01.Sample(sampler01, input.uv_0.xy);
	  float4 clr_arg2 = tex02.Sample(sampler02, input.uv_1.xyz);
	  float4 clr = clr_arg1 + clr_arg2 - clr_arg1 * clr_arg2;
	  
	  return float4(clr.r * emissive.r, clr.g * emissive.g, clr.b * emissive.b, clr.a * emissive.a);
	}
	
    ]]></Shader>
  <!--devicetype:dx9;dx11;gl;gles-->
  <!--devicetype=dx9,capability:ps_2_0; ps_2_x; vs_2_0; vs_2_x;ps_3_0;vs_3_0-->
  <!--必须有name,shadertype，devicetype; shadertype:hlsl;cg;glsl;目前实现了hlsl-->
  <Technique name="modulate">
    <Pass name="modulate" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsModulate" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
  <Technique name="modulate2x">
    <Pass name="modulate2x" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsModulate2X" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
  <Technique name="modulate4x">
    <Pass name="modulate4x" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsModulate4X" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
  <Technique name="add">
    <Pass name="add" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsAdd" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
  <Technique name="addsigned">
    <Pass name="addsigned" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsAddSigned" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
  <Technique name="addsigned2x">
    <Pass name="addsigned2x" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsAddSigned2X" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
  <Technique name="subtract">
    <Pass name="subtract" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsSubtract" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
  <Technique name="addsmooth">
    <Pass name="addsmooth" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0" />
      <State name="PixelShader" value="PsAddSmooth" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0" />
    </Pass>
  </Technique>
</Effect>
