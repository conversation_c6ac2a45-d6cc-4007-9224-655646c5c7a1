ImageRuntimeVersion: v4.0.30319
Assembly DVNetWrapper, Version=1.0.*, Culture=固定语言(固定国家/地区): 
	hash=SHA1, flags=PublicKey
Assembly mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089: 
	hash=None, flags=None
Assembly System.Data, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089: 
	hash=None, flags=None
Assembly System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089: 
	hash=None, flags=None
Assembly System.Xml, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089: 
	hash=None, flags=None
Class DVNetWrapper.ManagedMsgParaValue: AutoLayout, AnsiClass, Class, Public, BeforeFieldInit
    Void .ctor(System.String, DVNetWrapper.ManagedMsgParaValueType, Double): PrivateScope, Public, HideBySig, SpecialName, RTSpecialName
    Void .ctor(System.String, DVNetWrapper.ManagedMsgParaValueType, System.String): PrivateScope, Public, HideBySig, SpecialName, RTSpecialName
    Void .ctor(System.String, DVNetWrapper.ManagedMsgParaValueType, Int32): PrivateScope, Public, HideBySig, SpecialName, RTSpecialName
    Void .ctor(System.String, DVNetWrapper.ManagedMsgParaValueType, UInt32): PrivateScope, Public, HideBySig, SpecialName, RTSpecialName
    Void .ctor(System.String, DVNetWrapper.ManagedMsgParaValueType, Single): PrivateScope, Public, HideBySig, SpecialName, RTSpecialName
  Properties:
    System.Object Value 'get set' : 
    DVNetWrapper.ManagedMsgParaValueType ValueType 'get set' : 
    System.String Name 'get set' : 
  Methods:
    get_Name(): PrivateScope, Public, HideBySig, SpecialName
    set_Name(String): PrivateScope, Public, HideBySig, SpecialName
    get_ValueType(): PrivateScope, Public, HideBySig, SpecialName
    set_ValueType(ManagedMsgParaValueType): PrivateScope, Public, HideBySig, SpecialName
    get_Value(): PrivateScope, Public, HideBySig, SpecialName
    set_Value(Object): PrivateScope, Public, HideBySig, SpecialName
Class DVNetWrapper.ManagedElevatorSpecification: AutoLayout, AnsiClass, Class, Public, BeforeFieldInit
    Void .ctor(): PrivateScope, Public, HideBySig, SpecialName, RTSpecialName
  Properties:
    Int32 ElevatorCount 'get set' : 
    Int32 DoorHeight 'get set' : 
    Int32 DoorWidth 'get set' : 
    Int32 Height 'get set' : 
    Int32 Depth 'get set' : 
    Int32 Width 'get set' : 
    Int32 ElevatorSizeId 'get set' : 
  Methods:
    get_ElevatorSizeId(): PrivateScope, Public, HideBySig, SpecialName
    set_ElevatorSizeId(Int32): PrivateScope, Public, HideBySig, SpecialName
    get_Width(): PrivateScope, Public, HideBySig, SpecialName
    set_Width(Int32): PrivateScope, Public, HideBySig, SpecialName
    get_Depth(): PrivateScope, Public, HideBySig, SpecialName
    set_Depth(Int32): PrivateScope, Public, HideBySig, SpecialName
    get_Height(): PrivateScope, Public, HideBySig, SpecialName
    set_Height(Int32): PrivateScope, Public, HideBySig, SpecialName
    get_DoorWidth(): PrivateScope, Public, HideBySig, SpecialName
    set_DoorWidth(Int32): PrivateScope, Public, HideBySig, SpecialName
    get_DoorHeight(): PrivateScope, Public, HideBySig, SpecialName
    set_DoorHeight(Int32): PrivateScope, Public, HideBySig, SpecialName
    get_ElevatorCount(): PrivateScope, Public, HideBySig, SpecialName
    set_ElevatorCount(Int32): PrivateScope, Public, HideBySig, SpecialName
Interface DVNetWrapper.IDecorationVRCallBack: AutoLayout, AnsiClass, Class, Public, ClassSemanticsMask, Abstract, BeforeFieldInit
  Methods:
    LoadCompleteCallBack(Int32, Int32, String): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    DownloadData(String, String): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    DownloadFile(String, String): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    OnProgress(Int32, Int32, String): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    GetReceiveProgress(): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
    SetReceiveProgress(Boolean(HasFieldMarshal)): PrivateScope, Public, Virtual, HideBySig, VtableLayoutMask, Abstract
Class DVNetWrapper.DVArrayVisitor: AutoLayout, AnsiClass, Class, Public, BeforeFieldInit
  Methods:
    GetElementInt(Int32, Int32): PrivateScope, Public, HideBySig
    SetElementValue(Int32, Int32, String): PrivateScope, Public, HideBySig
    SetElementValue(Int32, Int32, Single): PrivateScope, Public, HideBySig
    SetElementValue(Int32, Int32, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetElementValue(Int32, Int32, Int32): PrivateScope, Public, HideBySig
    GetElementBool(Int32, Int32): PrivateScope, Public, HideBySig
    GetElementFloat(Int32, Int32): PrivateScope, Public, HideBySig
    GetElementString(Int32, Int32): PrivateScope, Public, HideBySig
    GetElementCompound(Int32, Int32, Int32): PrivateScope, Public, HideBySig
    SetElementCompound(Int32, Int32, Int32, String): PrivateScope, Public, HideBySig
    GetRowCount(): PrivateScope, Public, HideBySig
    GetColumnCount(): PrivateScope, Public, HideBySig
    AddRow(): PrivateScope, Public, HideBySig
    DeleteRow(Int32): PrivateScope, Public, HideBySig
    Clear(): PrivateScope, Public, HideBySig
    PrintData(String): PrivateScope, Public, HideBySig
Class DVNetWrapper.DVIInitInterfaceForMT: AutoLayout, AnsiClass, Class, Public, BeforeFieldInit
  Methods:
    InitEngine(): PrivateScope, Public, HideBySig
    SetResourcesPath(String): PrivateScope, Public, HideBySig
    InitializeForRenderFrameC(): PrivateScope, Public, HideBySig
    RenderFrameC(): PrivateScope, Public, HideBySig
    SignalRenderThreadQuited(): PrivateScope, Public, HideBySig
    CreateRender(IntPtr, Int32, Int32, Int32): PrivateScope, Public, HideBySig
    LoadScene(): PrivateScope, Public, HideBySig
    InitElevator(ManagedElevatorSpecification): PrivateScope, Public, HideBySig
    FinalizeEngine(): PrivateScope, Public, HideBySig
Class DVNetWrapper.DecorationVRInterface: AutoLayout, AnsiClass, Class, Public, BeforeFieldInit
    Void .ctor(): PrivateScope, Public, HideBySig, SpecialName, RTSpecialName
  Methods:
    CreateInterfaceForMT(): PrivateScope, Public, HideBySig
    IsInitialize(): PrivateScope, Public, HideBySig
    InitializeElevator(IntPtr, ManagedElevatorSpecification, ManagedInitType, String, Int32, Int32): PrivateScope, Public, HideBySig
    SetDir(String, String): PrivateScope, Public, HideBySig
    ChangeElevatorSpecification(ManagedElevatorSpecification): PrivateScope, Public, HideBySig
    Finilize(): PrivateScope, Public, HideBySig
    SetPartByString(String): PrivateScope, Public, HideBySig
    SetPart(Int32, Int64, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetMaterial(Int32, Int64, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetPartByGoodsId(Int32, Int64, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetMaterialByGoodsId(Int32, Int64, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetHandrailPos(Int32, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetCopButtonCount(Int32, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetSubitemMaterial(Int32, Int32, Int64, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetElectricPartOrientation(Int32, Int32, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetElectricPartPos(Int32, Single, Single, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetMirrorSetupInfo(ManagedMirrorSetupPos, Single, Single, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    ClearOptionalPart(ManagedPartTypeId, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetDoorOpenType(Int32, Int32, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetHandrailPosMark(Int32, Int32): PrivateScope, Public, HideBySig
    Resize(Single, Single): PrivateScope, Public, HideBySig
    NotifyVr(): PrivateScope, Public, HideBySig
    DoChange(Int32, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    SetSkipDoChange(Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    IsSkipDoChange(): PrivateScope, Public, HideBySig
    AutoSetFCopSetupPos(Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    IsAutoSetFCopSetupPos(): PrivateScope, Public, HideBySig
    SetRemoveSkirting(Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    IsRemoveSkirting(): PrivateScope, Public, HideBySig
    GoToCar(): PrivateScope, Public, HideBySig
    GoToHall(): PrivateScope, Public, HideBySig
    DoVrProcess(): PrivateScope, Public, HideBySig
    SendVrMessage(String, ManagedMsgParaValue[], Int32): PrivateScope, Public, HideBySig
    SendVrMessage(String, Int32): PrivateScope, Public, HideBySig
    SetSceneFilePath(String): PrivateScope, Public, HideBySig
    SetResourcePath(String): PrivateScope, Public, HideBySig
    GetCurrentPartId(Int32): PrivateScope, Public, HideBySig
    GetCurrentPartName(Int32): PrivateScope, Public, HideBySig
    GetCurrentMaterialId(Int32): PrivateScope, Public, HideBySig
    GetElevatorSizeId(): PrivateScope, Public, HideBySig
    GetHandrailPos(): PrivateScope, Public, HideBySig
    GetCopButtonCount(): PrivateScope, Public, HideBySig
    GetCopPanelType(Int32): PrivateScope, Public, HideBySig
    GetSubitemMaterial(Int32, Int32): PrivateScope, Public, HideBySig
    GetElectricPartOrientation(Int32): PrivateScope, Public, HideBySig
    GetElectricPartPosX(Int32): PrivateScope, Public, HideBySig
    GetElectricPartPosY(Int32): PrivateScope, Public, HideBySig
    GetWidth(): PrivateScope, Public, HideBySig
    GetDepth(): PrivateScope, Public, HideBySig
    GetDoorWidth(): PrivateScope, Public, HideBySig
    GetDoorHeight(): PrivateScope, Public, HideBySig
    GetHeight(): PrivateScope, Public, HideBySig
    SetDecorationVrCallBack(IDecorationVRCallBack): PrivateScope, Public, HideBySig
    ResizeRender(Int32, Int32): PrivateScope, Public, HideBySig
    RenderLargeImage(String, Single, Single): PrivateScope, Public, HideBySig
    RenderPanoramaImages(String, Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    RenderPanoramaImages(String): PrivateScope, Public, HideBySig
    Print(String): PrivateScope, Public, HideBySig
    SetSnapPos(Int32): PrivateScope, Public, HideBySig
    GetSceneArrayVisitor(String): PrivateScope, Public, HideBySig
    SetDownloadServerURL(String, String): PrivateScope, Public, HideBySig
    SetLoginInfo(Int64, Int32, Int64): PrivateScope, Public, HideBySig
    SetIsStandalongVersion(Boolean(HasFieldMarshal)): PrivateScope, Public, HideBySig
    GetWallElemCount(Int32): PrivateScope, Public, HideBySig
    SendVrMessageWithParas(String, String, Int32): PrivateScope, Public, HideBySig
    GetCurrentTotalMemory(): PrivateScope, Public, HideBySig
    GetCurrentPeakMeomry(): PrivateScope, Public, HideBySig
    PrintMemoryUsedLog(String): PrivateScope, Public, HideBySig
    RenderLargeImageWithFixedViewPos(Int32, Int32, Int32): PrivateScope, Public, HideBySig
Struct DVNetWrapper.ManagedInitType: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedInitType IT_NEED_CREATE = 0 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedInitType IT_NEED_INIT = 1 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedInitType IT_DIRECT_USE = 2 : Public, Static, Literal, HasDefault
Struct DVNetWrapper.ManagedMsgParaValueType: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedMsgParaValueType MPVT_INT32 = 0 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedMsgParaValueType MPVT_UINT32 = 1 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedMsgParaValueType MPVT_FLOAT32 = 2 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedMsgParaValueType MPVT_DOUBLE = 3 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedMsgParaValueType MPVT_STRING = 4 : Public, Static, Literal, HasDefault
Struct DVNetWrapper.ManagedPartTypeId: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedPartTypeId ALL = -123456 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId AllWall = -123457 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeCarConfig = 100000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHallConfig = 200000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeElevatorConfig = 300000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeElevatorSetting = 300001 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeElevatorSize = 300002 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeElevatorRGBVrBackground = 9000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeLeftAccessory = 1100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeBackAccessory = 2100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeRightAccessory = 3100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeLoadingSightseeing = 30007 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeTop = 100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeBottom = 200 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeFrontWall = 300 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeFrontWallConstructorType = 310 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeCopWall = 350 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeDoorHeader = 400 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEntranceColumn = 500 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeCarDoor = 600 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeCarDoorTrim = 610 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeTrim = 1000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeLeftProtectiveWall = 1310 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeRightProtectiveWall = 1320 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeBackProtectiveWall = 1330 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHandrail = 1500 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeMirror = 1600 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeCop = 2000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeAuxCop = 2010 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeCopDisplay = 2300 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeCopButton = 2301 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeAuxCopDisplay = 2400 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeAuxCopButton = 2401 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeCarIndicator = 3000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHDCop = 4000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHDCopDisplay = 4100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHDCopButton = 4101 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeMonitor = 5000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeLeftWall = 6700 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeRightWall = 6800 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeBackWall = 6900 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEMIDS = 7100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEMIDSDisplay = 7200 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeSkirting = 7000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeJamb = 10000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHallDoor = 10100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHallDoorTrim = 10110 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeLop = 12000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeLopButton = 12001 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeLopDisplay = 12200 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHIBPlate = 12300 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeLantern = 13000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeLanternDisplay = 13100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHallIndicator = 14000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHallIndicatorDisplay = 14100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHallIndicatorPlate = 14200 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHallWall = 15000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeHallBottom = 15100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeButton = 10 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeMaterial = 1 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscalatorConfig = 600000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaStep = 500000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaStepLighting = 500050 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaHandrail = 500100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaHandrailLighting = 500150 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaHandrailGuid = 500200 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaHandrailEnter = 500300 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaAcessOver = 500400 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaAcessOverFlag = 500500 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaBalustrade = 500600 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaComb = 500700 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaCombLighting = 500750 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaDecking = 500800 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaSkirt = 500900 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaSkirtLighting = 500950 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaSkirtLightingType = 500910 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaSkirtBrush = 501000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaTrafficLight = 501050 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaSideCladding = 501100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaPiece = 501200 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaStepFloorNumber = 500410 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaSideCladdingPit = 501110 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaSideCladdingLighting = 501120 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaPhotoelectric = 501300 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaOtherParts = 501400 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaAcessOverExpendType = 501500 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaCenterBrace = 501600 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaScenes = 502000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaScenesType = 502100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaScenesArrangement = 502200 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaDeckingCtrlBox = 500810 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId PartTypeEscaTrussLighting = 503100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId HoistWayConfig = 1000000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId TractionMachine = 1000001 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId ControlCabinet = 1000002 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId ImageGroup_HoistWay = 2000001 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId ImageGroup_Car = 2000002 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedPartTypeId ImageGroup_Hall = 2000003 : Public, Static, Literal, HasDefault
Struct DVNetWrapper.ManagedConstWallElemMark: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedConstWallElemMark WALLELEM_MARK_STANDARD_MAX = 5000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedConstWallElemMark WALLELEM_MARK_SKIRTING = 5100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedConstWallElemMark WALLELEM_MARK_SKIRTING_AROUND = 5200 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedConstWallElemMark WALLELEM_MARK_SLUG = 5300 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedConstWallElemMark WALLELEM_MARK_GLASS_TB_DAGE = 5400 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedConstWallElemMark WALLELEM_MARK_GLASS_LR_DAGE = 5410 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedConstWallElemMark WALLELEM_MARK_COP = 5500 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedConstWallElemMark WALLELEM_MARK_REARWALL_MIRRORMATERIAL = 5600 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedConstWallElemMark WALLELEM_MARK_LEFTPANEL = 1 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedConstWallElemMark WALLELEM_MARK_CENTERPANEL = 2 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedConstWallElemMark WALLELEM_MARK_RIGHTPANEL = 3 : Public, Static, Literal, HasDefault
Struct DVNetWrapper.ManagedCommonMaterialId: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedCommonMaterialId MATERIAL_ST4 = 200 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCommonMaterialId MATERIAL_MP1 = 201 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCommonMaterialId MATERIAL_MP2 = 211 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCommonMaterialId MATERIAL_ST2 = 210 : Public, Static, Literal, HasDefault
Struct DVNetWrapper.ManagedHandrailSetupPos: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedHandrailSetupPos HSP_NONE = 0 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedHandrailSetupPos HSP_BACK = 1 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedHandrailSetupPos HSP_LEFT = 2 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedHandrailSetupPos HSP_LEFT_BACK = 3 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedHandrailSetupPos HSP_RIGHT = 4 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedHandrailSetupPos HSP_RIGHT_BACK = 5 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedHandrailSetupPos HSP_LEFT_RIGHT = 6 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedHandrailSetupPos HSP_LEFT_RIGHT_BACK = 7 : Public, Static, Literal, HasDefault
Struct DVNetWrapper.ManagedMirrorSetupPos: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedMirrorSetupPos MSP_NONE = 0 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedMirrorSetupPos MSP_BACK = 2000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedMirrorSetupPos MSP_LEFT = 1000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedMirrorSetupPos MSP_RIGHT = 3000 : Public, Static, Literal, HasDefault
Struct DVNetWrapper.ManagedCameraPos: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedCameraPos CP_FRONT_WALL = 101 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_FRONT_WALL_LEFT45 = 102 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_FRONT_WALL_RIGHT45 = 108 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_BACK_WALL = 105 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_BACK_WALL_LEFT45 = 106 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_BACK_WALL_RIGHT45 = 104 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_FRONT_WALL_CAR_FILL_VIEW = 121 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_HALL_NORMAL = 1001 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_HALL_VIEW_DETAIL = 1002 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_HALL_VIEW_ONE_DOOR = 1003 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_CAR_NORMAL = 2001 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_CAR_MAX = 3000 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_ESCALATOR_VIEW1 = 10001 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_ESCALATOR_VIEW2 = 10002 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_ESCALATOR_VIEW3 = 10003 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_ESCALATOR_VIEW4 = 10004 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_ESCALATOR_VIEW5 = 10005 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCameraPos CP_ESCALATOR_MAX = 11000 : Public, Static, Literal, HasDefault
Struct DVNetWrapper.ManagedCopSetupOrientation: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedCopSetupOrientation CSP_COP_CENTER = 4 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCopSetupOrientation CSP_FRONT_CENTER = 2 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCopSetupOrientation CSP_LEFT = 8 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedCopSetupOrientation CSP_RIGHT = 16 : Public, Static, Literal, HasDefault
Struct DVNetWrapper.ManagedDoorOpenType: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedDoorOpenType DOT_CENTER = 100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedDoorOpenType DOT_L2R = 201 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedDoorOpenType DOT_R2L = 202 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedDoorOpenType DOT_FAKE_SCENE_DOOR = 1000 : Public, Static, Literal, HasDefault
Struct DVNetWrapper.ManagedLopSetupOrientation: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedLopSetupOrientation CALL_SETUP_DOOR_LEFT = 0 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedLopSetupOrientation CALL_SETUP_DOOR_RIGHT = 1 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedLopSetupOrientation CALL_SETUP_PARRELLEL = 2 : Public, Static, Literal, HasDefault
Struct DVNetWrapper.ManagedHIHLSetupOrientation: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedHIHLSetupOrientation HL_SETUP_DoorTop = 0 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedHIHLSetupOrientation HL_SETUP_DoorLeft = 1 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedHIHLSetupOrientation HL_SETUP_DoorRight = 2 : Public, Static, Literal, HasDefault
Struct DVNetWrapper.ManagedFrontWallType: AutoLayout, AnsiClass, Class, Public, Sealed, BeforeFieldInit
  :System.Enum
  Fields:
    Int32 value__ : Public, SpecialName, RTSpecialName
    DVNetWrapper.ManagedFrontWallType FWT_NONE = 0 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_100 = 100 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_200 = 200 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_230 = 230 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_300 = 300 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_330 = 330 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_500 = 500 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_510 = 510 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_520 = 520 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_600 = 600 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_610 = 610 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_620 = 620 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_700 = 700 : Public, Static, Literal, HasDefault
    DVNetWrapper.ManagedFrontWallType FWT_1000 = 1000 : Public, Static, Literal, HasDefault
