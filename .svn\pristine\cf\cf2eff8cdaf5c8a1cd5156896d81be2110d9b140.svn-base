<?xml version="1.0" encoding="utf-8"?>
<Effect name="usr/decoration/shader_mirror_channel_cube">
  <!--必须有name,type；semantic，default可选-->
  <Parameter name="vp" type="Matrix4f" semantic="ViewProj"/>
  <!--<Parameter name="wv" type="Matrix4f" semantic="WorldView"/>-->
  <Parameter name="world" type="Matrix4f" semantic="World"/>
  <Parameter name="inv_world_transpose" type="Matrix4f" semantic="InverseWorldTranspose"/>
  <Parameter name="camera_world_pos" type="Vector3f" semantic="CameraWorldPosition"/>
  <Parameter name="tex_01" type="Texture2D" semantic="Texture0"/>
  <Parameter name="tex_lm" type="Texture2D" semantic="Texture1"/>
  <Parameter name="tex_ref" type="TextureCube" semantic="Texture2"/>
  <Parameter name="tex_channel" type="Texture2D" semantic="Texture3"/>
  <Parameter name="vec_tex" type="Vector4f" default= "1,1,0,0" />
  <Parameter name="vec_ori" type="Vector4f" default= "1,1,0,0" />
  <Parameter name="vec_channel" type="Vector4f" default= "1,1,0,0" />
  <!--
  reserve this parameter for compatiable with hairline_stainless_steel
  x, unused; 
  y, unused; 
  z, is_hairline 0:hair line; 1:paint;
  -->
  <Parameter name="setting" type="Vector4f" default="1,1,0,0"/>
  <Parameter name="to_box_world_trans" type="Matrix4f"/>
  <Parameter name="brightness_ambient" type="Colorf" default= "1,1,1,1" />
  <Parameter name="reflect_strength_diffuse" type="Colorf" default= "1,1,1,1" />
  <Parameter name="plate_emissive" type="Colorf" default= "1,1,1,1" />
  <Parameter name="box_min" type="Vector4f" default= "1,1,0,0"/>
  <Parameter name="box_max" type="Vector4f" default= "1,1,0,0"/>
  <Parameter name="box_center" type="Vector4f" default= "1,1,0,-1"/>
  <Sampler name="sampler_tex">
    <Texture value="tex_01"/>
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR"/>
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP"/>
    <AddressV value="WRAP"/>
    <AddressW value="WRAP"/>
  </Sampler>
  <Sampler name="sampler_lm">
    <Texture value="tex_lm"/>
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR"/>
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="CLAMP"/>
    <AddressV value="CLAMP"/>
    <AddressW value="CLAMP"/>
  </Sampler>
  <Sampler name="sampler_channel">
    <Texture value="tex_channel"/>
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR"/>
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP"/>
    <AddressV value="WRAP"/>
    <AddressW value="WRAP"/>
  </Sampler>
  <Sampler name="sampler_ref">
    <Texture value="tex_ref"/>
    <!--取值范围参考TextureFilterMode,去掉前缀TFM_-->
    <Filter value="MIN_MAG_MIP_LINEAR"/>
    <!--AddressUVW取值范围参考D3DTEXTUREADDRESS，去掉前缀D3DTADDRESS_-->
    <AddressU value="WRAP"/>
    <AddressV value="WRAP"/>
    <AddressW value="WRAP"/>
  </Sampler>
	<Shader type="glsl">
		<Include file="shader_buildin/gl_inc.h"/>
		<GLShader name="VsMain" type="Vertex">
			<![CDATA[	
			uniform mat4 vp;
			//uniform mat4 v;
			uniform mat4 world;
			uniform mat4 inv_world_transpose;
			uniform vec3 camera_world_pos;
			
			uniform vec4 vec_tex;   
			uniform vec4 vec_ori; 
			uniform vec4  vec_channel;
      
			uniform mat4 to_box_world_trans;
			uniform mediump vec4 setting;   
			uniform vec4 box_min;   
			uniform vec4 box_max;   
			uniform vec4 box_center;      
			
			attribute vec4 a_position; 
			attribute vec3 a_normal;
			attribute vec2 a_uv0; 
		
			varying vec4 tc_ref; 
			varying vec2 tc_lm;
			varying vec2 tc_tex;
			varying vec2 tc_channel;
			varying vec3 v_normal;
      varying vec4 v_pos_in_box;
      
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;	
	
				ComputeProbeBoxWorldInfo(tc_ref, v_normal, v_pos_in_box, world, inv_world_transpose, to_box_world_trans, 
        a_position, camera_world_pos, a_normal);

			  tc_lm      = a_uv0;		
			  tc_tex.xy  = ROTATE_TEXCOORD(a_uv0,vec_ori);	
			  tc_tex.xy  = TRANSFORM_TEXCOORD(tc_tex, vec_tex);
			  tc_channel.xy  = TRANSFORM_TEXCOORD(a_uv0, vec_channel);
			}
			]]>
		</GLShader>
		<GLShader name="PsMain" type="Pixel">
			<![CDATA[
			//precision mediump float;
			
			uniform sampler2D sampler_tex;
			uniform sampler2D sampler_lm;
			uniform sampler2D sampler_channel;
			uniform samplerCube sampler_ref;
			

			uniform vec4 brightness_ambient;
			uniform vec4 reflect_strength_diffuse;
			uniform vec4 plate_emissive;
      
			uniform mat4 to_box_world_trans;
			uniform mediump vec4 setting;   
			uniform vec4 box_min;   
			uniform vec4 box_max;   
			uniform vec4 box_center;      
							
			varying vec4 tc_ref; 
			varying vec2 tc_lm;
			varying vec2 tc_tex;
			varying vec2 tc_channel;
			varying vec3 v_normal;
      varying vec4 v_pos_in_box;
	
			void main()
			{ 
			  vec3 uv = normalize(reflect(v_pos_in_box.xyz - tc_ref.xyz, v_normal));
			  vec4 col_ref = EnvMapping(sampler_ref, v_pos_in_box.xyz, uv, box_min, box_max, box_center);			
				
				vec4 col_lm        = texture2D( sampler_lm, tc_lm) ;
				vec4 col_tex       = texture2D( sampler_tex, tc_tex) ;
				vec4 col_channel   =  texture2D( sampler_channel, tc_channel);
				vec4  clr_res       = col_lm * ( col_tex * plate_emissive + col_ref * reflect_strength_diffuse ) * brightness_ambient * 2.0; 
				clr_res.w             = col_channel.w * col_tex.w ;
				gl_FragColor = clr_res;
			}
		]]>
		</GLShader>
	</Shader>
  <Shader type="glsl3">
    <Include file="shader_buildin/gl_inc.h"/>
    <GLShader name="VsMain" type="Vertex">
      <![CDATA[	
      layout (std140) uniform entity
      {
			mat4 world;
			mat4 inv_world_transpose;
			};
      layout (std140) uniform view
      {
			mat4 vp;
			vec3 camera_world_pos;
			};
      layout (std140) uniform material
      {
			vec4 vec_tex;   
			vec4 vec_ori; 
			vec4 vec_channel;
			vec4 brightness_ambient;
			vec4 reflect_strength_diffuse;
			vec4 plate_emissive;
			mat4 to_box_world_trans;
			mediump vec4 setting;   
			vec4 box_min;   
			vec4 box_max;   
			vec4 box_center;   
			};
      
			in vec4 a_position; 
			in vec3 a_normal;
			in vec2 a_uv0; 
		
			out vec4 tc_ref; 
			out vec2 tc_lm;
			out vec2 tc_tex;
			out vec2 tc_channel;
			out vec3 v_normal;
			out vec4 v_pos_in_box;
      
			void main() 
			{
				vec4 r0 = a_position * MUL(world, vp);
				gl_Position = r0;	
	
				ComputeProbeBoxWorldInfo(tc_ref, v_normal, v_pos_in_box, world, inv_world_transpose, to_box_world_trans, 
        a_position, camera_world_pos, a_normal);

			  tc_lm      = a_uv0;		
				tc_tex.xy  = ROTATE_TEXCOORD(a_uv0, vec_ori);	
				tc_tex.xy  = TRANSFORM_TEXCOORD(tc_tex, vec_tex);	
			  tc_channel.xy  = TRANSFORM_TEXCOORD(a_uv0, vec_channel);
			}
			]]>
    </GLShader>
    <GLShader name="PsMain" type="Pixel">
      <![CDATA[
			precision mediump float;
			
			uniform sampler2D sampler_tex;
			uniform sampler2D sampler_lm;
			uniform sampler2D sampler_channel;
			uniform samplerCube sampler_ref;
						
			layout (std140) uniform material
      {
			vec4 vec_tex;   
			vec4 vec_ori; 
			vec4 vec_channel;
			vec4 brightness_ambient;
			vec4 reflect_strength_diffuse;
			vec4 plate_emissive;
			mat4 to_box_world_trans;
			mediump vec4 setting;   
			vec4 box_min;   
			vec4 box_max;   
			vec4 box_center;   
			};			
      
			in vec4 tc_ref; 
			in vec2 tc_lm;
			in vec2 tc_tex;
			in vec2 tc_channel;
			in vec3 v_normal;
			in vec4 v_pos_in_box;
			out vec4 fragColor;
      
			void main()
			{ 
			  vec3 uv = normalize(reflect(v_pos_in_box.xyz - tc_ref.xyz, v_normal));
			  vec4 col_ref = EnvMapping(sampler_ref, v_pos_in_box.xyz, uv, box_min, box_max, box_center);			
				
				vec4 col_lm        = texture( sampler_lm, tc_lm) ;
				vec4 col_tex       = texture( sampler_tex, tc_tex) ;
				vec4 col_channel   =  texture( sampler_channel, tc_channel);
				vec4  clr_res       = col_lm * ( col_tex * plate_emissive + col_ref * reflect_strength_diffuse ) * brightness_ambient * 2.0; 
				clr_res.w             = col_channel.w * col_tex.w ;
				fragColor = clr_res;
			}
		]]>
    </GLShader>
  </Shader>
  <Shader type="hlsl5">
    <Include file="shader_buildin/dx_inc.h"/>
    <![CDATA[
    cbuffer cbEntity : register( b0 )
    {
	  float4x4 world;
	  float4x4 inv_world_transpose;	
    };
    cbuffer cbView : register( b2 )
    {
    matrix vp;
	  float3 camera_world_pos;
    };

    cbuffer cbMaterial : register( b1 )
    {	
		matrix to_box_world_trans;
    float4 vec_tex;   
	  float4 vec_ori;
	  float4 vec_channel;
	  float4 brightness_ambient;
	  float4 reflect_strength_diffuse;
	  float4 plate_emissive;
		float4 setting;
    float4 box_min;
    float4 box_max;
    float4 box_center;
    };
	
	
	  SamplerState sampler_tex : register( s0 );
    Texture2D tex_01 : register( t0 );
    
    SamplerState sampler_lm : register( s1 );
    Texture2D tex_lm : register( t1 );
    
    SamplerState sampler_ref : register( s2 );
    TextureCube tex_ref : register( t2 );
	
    SamplerState sampler_channel : register( s3 );
    Texture2D tex_channel : register( t3 );
		
    struct VsInput
    {
		float3     pos:POSITION;
		float2     tc :TEXCOORD; 
		float3     normal : NORMAL;
    };
    
    struct VsOutput
    {
		float4   pos             :   SV_POSITION ;
		float4   tc_ref          :   TEXCOORD0;
		float2   tc_lm           :   TEXCOORD1;
		float2   tc_tex          :   TEXCOORD2;
		float2   tc_channel      :   TEXCOORD3;	
		float3   normal          :   TEXCOORD4;
    float4   pos_in_box      :   TEXCOORD5;
    };
    
    VsOutput VsMain(VsInput input)
    {
	    VsOutput o = (VsOutput)0;
		
		  float4   r0=mul(float4(input.pos,1),MUL(world, vp));
		  o.pos=r0;	
      
      //float4x4 wvp1 = mul(wv, tp);
			//o.tc_ref     = mul(float4(input.pos,1),wvp1);	
      ComputeProbeBoxWorldInfo(o.tc_ref, o.normal, o.pos_in_box, world, inv_world_transpose, to_box_world_trans, 
        input.pos, camera_world_pos, input.normal);
        			
			o.tc_lm      = input.tc;		
			o.tc_tex.xy  = ROTATE_TEXCOORD(input.tc.xy,vec_ori);	
			o.tc_tex.xy  = TRANSFORM_TEXCOORD(o.tc_tex.xy, vec_tex);	
			o.tc_channel.xy  = TRANSFORM_TEXCOORD(input.tc.xy,vec_channel);

	    return o;
    }
	
    float4 PsMain(VsOutput input) : SV_Target
    {
			float3 uv = normalize(reflect(input.pos_in_box.xyz - input.tc_ref.xyz, input.normal));
			float4 col_ref = EnvMapping(sampler_ref, tex_ref, input.pos_in_box, uv, box_min, box_max, box_center);
		
		  float4 col_lm        = tex_lm.Sample( sampler_lm,input.tc_lm) ;
		  float4 col_tex       = tex_01.Sample( sampler_tex,input.tc_tex) ;
		  float4 col_channel   =  tex_channel.Sample( sampler_channel,input.tc_channel);
		  float4  clr_res       = col_lm * ( col_tex * plate_emissive + col_ref * reflect_strength_diffuse ) * brightness_ambient * 2; 
		  clr_res.w             = col_channel.w * col_tex.w ;

		  return clr_res;
    }
    ]]>
  </Shader>
  <Technique name="tech">
    <Pass name="pass" restorestate="false">
      <State name="VertexShader" value="VsMain" dx9_profile="vs_2_0" dx11_profile="vs_4_0" gles_profile="vs_1_0"/>
      <State name="PixelShader" value="PsMain" dx9_profile="ps_2_0" dx11_profile="ps_4_0" gles_profile="ps_1_0"/>
      <State name="DepthBias" value="-1"/>
    </Pass>
  </Technique>
</Effect>
